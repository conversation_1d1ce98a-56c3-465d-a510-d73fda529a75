# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "CMakeFiles/3.29.0/CMakeCCompiler.cmake"
  "CMakeFiles/3.29.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.29.0/CMakeSystem.cmake"
  "/home/<USER>/Projects/indi/CMakeLists.txt"
  "/home/<USER>/Projects/indi/cmake_modules/CMakeCommon.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/CMakeParseArguments.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindCFITSIO.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindFFTW3.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindGMock.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindGSL.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindJPEG.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindLibXISF.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindLibev.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindNova.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindOggTheora.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindPackageHandleStandardArgs.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindPackageMessage.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindRTLSDR.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/FindUSB1.cmake"
  "/home/<USER>/Projects/indi/cmake_modules/UnityBuild.cmake"
  "/home/<USER>/Projects/indi/config-usb.h.cmake"
  "/home/<USER>/Projects/indi/config.h.cmake"
  "/home/<USER>/Projects/indi/drivers/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/agent/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/auxiliary/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/ccd/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/dome/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/filter_wheel/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/focuser/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/io/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/receiver/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/rotator/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/spectrograph/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/telescope/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/video/CMakeLists.txt"
  "/home/<USER>/Projects/indi/drivers/weather/CMakeLists.txt"
  "/home/<USER>/Projects/indi/examples/CMakeLists.txt"
  "/home/<USER>/Projects/indi/examples/tutorial_eight/CMakeLists.txt"
  "/home/<USER>/Projects/indi/examples/tutorial_five/CMakeLists.txt"
  "/home/<USER>/Projects/indi/examples/tutorial_four/CMakeLists.txt"
  "/home/<USER>/Projects/indi/examples/tutorial_one/CMakeLists.txt"
  "/home/<USER>/Projects/indi/examples/tutorial_seven/CMakeLists.txt"
  "/home/<USER>/Projects/indi/examples/tutorial_six/CMakeLists.txt"
  "/home/<USER>/Projects/indi/examples/tutorial_three/CMakeLists.txt"
  "/home/<USER>/Projects/indi/examples/tutorial_two/CMakeLists.txt"
  "/home/<USER>/Projects/indi/indiserver/CMakeLists.txt"
  "/home/<USER>/Projects/indi/indiversion.h.cmake"
  "/home/<USER>/Projects/indi/libindi.pc.cmake"
  "/home/<USER>/Projects/indi/libs/alignment/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/dsp/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/eventloop/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/fpack/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/hid/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/indiabstractclient/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/indibase/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/indiclient/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/indicore/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/indicore/indiapi.h.in"
  "/home/<USER>/Projects/indi/libs/indidevice/CMakeLists.txt"
  "/home/<USER>/Projects/indi/libs/sockets/CMakeLists.txt"
  "/home/<USER>/Projects/indi/tools/CMakeLists.txt"
  "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestConfig.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestConfigVersion.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestTargets-release.cmake"
  "/usr/lib/x86_64-linux-gnu/cmake/GTest/GTestTargets.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeCCompiler.cmake.in"
  "/usr/local/share/cmake-3.29/Modules/CMakeCCompilerABI.c"
  "/usr/local/share/cmake-3.29/Modules/CMakeCInformation.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/local/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/local/share/cmake-3.29/Modules/CMakeCXXInformation.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeDetermineSystem.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeFindBinUtils.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeFindDependencyMacro.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeGenericSystem.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeInitializeConfigs.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeLanguageInformation.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeParseLibraryArchitecture.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakePushCheckState.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeSystem.cmake.in"
  "/usr/local/share/cmake-3.29/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeUnixFindMake.cmake"
  "/usr/local/share/cmake-3.29/Modules/CTest.cmake"
  "/usr/local/share/cmake-3.29/Modules/CTestTargets.cmake"
  "/usr/local/share/cmake-3.29/Modules/CTestUseLaunchers.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckCCompilerFlag.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckCSourceCompiles.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckIncludeFile.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckLibraryExists.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckSymbolExists.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GNU-C.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GNU-CXX.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GNU.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/local/share/cmake-3.29/Modules/DartConfiguration.tcl.in"
  "/usr/local/share/cmake-3.29/Modules/FeatureSummary.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindCURL.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindGTest.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindIconv.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindPackageMessage.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindPkgConfig.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindThreads.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindZLIB.cmake"
  "/usr/local/share/cmake-3.29/Modules/GNUInstallDirs.cmake"
  "/usr/local/share/cmake-3.29/Modules/GoogleTest.cmake"
  "/usr/local/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "/usr/local/share/cmake-3.29/Modules/Internal/CheckCompilerFlag.cmake"
  "/usr/local/share/cmake-3.29/Modules/Internal/CheckFlagCommonConfig.cmake"
  "/usr/local/share/cmake-3.29/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/local/share/cmake-3.29/Modules/Internal/FeatureTesting.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux-GNU.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux-Initialize.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/UnixPaths.cmake"
  "/usr/local/share/cmake-3.29/Modules/SelectLibraryConfigurations.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.29.0/CMakeSystem.cmake"
  "CMakeFiles/3.29.0/CMakeCCompiler.cmake"
  "CMakeFiles/3.29.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.29.0/CMakeCCompiler.cmake"
  "CMakeFiles/3.29.0/CMakeCXXCompiler.cmake"
  "DartConfiguration.tcl"
  "config-usb.h"
  "libindi.pc"
  "config.h"
  "indiversion.h"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/indicore/indiapi.h"
  "libs/indicore/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/indidevice/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/indiabstractclient/CMakeFiles/CMakeDirectoryInformation.cmake"
  "indiserver/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/sockets/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/indiclient/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/eventloop/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/dsp/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/fpack/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/hid/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/indibase/CMakeFiles/CMakeDirectoryInformation.cmake"
  "libs/alignment/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/telescope/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/ccd/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/focuser/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/rotator/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/filter_wheel/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/dome/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/auxiliary/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/io/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/receiver/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/weather/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/agent/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/video/CMakeFiles/CMakeDirectoryInformation.cmake"
  "drivers/spectrograph/CMakeFiles/CMakeDirectoryInformation.cmake"
  "tools/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/tutorial_one/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/tutorial_two/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/tutorial_three/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/tutorial_four/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/tutorial_five/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/tutorial_six/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/tutorial_seven/CMakeFiles/CMakeDirectoryInformation.cmake"
  "examples/tutorial_eight/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/Experimental.dir/DependInfo.cmake"
  "CMakeFiles/Nightly.dir/DependInfo.cmake"
  "CMakeFiles/Continuous.dir/DependInfo.cmake"
  "CMakeFiles/NightlyMemoryCheck.dir/DependInfo.cmake"
  "CMakeFiles/NightlyStart.dir/DependInfo.cmake"
  "CMakeFiles/NightlyUpdate.dir/DependInfo.cmake"
  "CMakeFiles/NightlyConfigure.dir/DependInfo.cmake"
  "CMakeFiles/NightlyBuild.dir/DependInfo.cmake"
  "CMakeFiles/NightlyTest.dir/DependInfo.cmake"
  "CMakeFiles/NightlyCoverage.dir/DependInfo.cmake"
  "CMakeFiles/NightlyMemCheck.dir/DependInfo.cmake"
  "CMakeFiles/NightlySubmit.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalStart.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalUpdate.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalConfigure.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalBuild.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalTest.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalCoverage.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalMemCheck.dir/DependInfo.cmake"
  "CMakeFiles/ExperimentalSubmit.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousStart.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousUpdate.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousConfigure.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousBuild.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousTest.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousCoverage.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousMemCheck.dir/DependInfo.cmake"
  "CMakeFiles/ContinuousSubmit.dir/DependInfo.cmake"
  "libs/indicore/CMakeFiles/indicore.dir/DependInfo.cmake"
  "libs/indidevice/CMakeFiles/indidevice.dir/DependInfo.cmake"
  "libs/indiabstractclient/CMakeFiles/indiabstractclient.dir/DependInfo.cmake"
  "indiserver/CMakeFiles/indiserver.dir/DependInfo.cmake"
  "libs/sockets/CMakeFiles/sockets.dir/DependInfo.cmake"
  "libs/indiclient/CMakeFiles/indiclient_OBJECT.dir/DependInfo.cmake"
  "libs/indiclient/CMakeFiles/indiclientstatic.dir/DependInfo.cmake"
  "libs/indiclient/CMakeFiles/indiclient.dir/DependInfo.cmake"
  "libs/eventloop/CMakeFiles/eventloop.dir/DependInfo.cmake"
  "libs/dsp/CMakeFiles/dsp.dir/DependInfo.cmake"
  "libs/fpack/CMakeFiles/fpack.dir/DependInfo.cmake"
  "libs/hid/CMakeFiles/hid.dir/DependInfo.cmake"
  "libs/hid/CMakeFiles/indi_hid_test.dir/DependInfo.cmake"
  "libs/indibase/CMakeFiles/indidriver_OBJECT.dir/DependInfo.cmake"
  "libs/indibase/CMakeFiles/indidriver_STATIC.dir/DependInfo.cmake"
  "libs/indibase/CMakeFiles/indidriver.dir/DependInfo.cmake"
  "libs/alignment/CMakeFiles/AlignmentDriver.dir/DependInfo.cmake"
  "libs/alignment/CMakeFiles/AlignmentClient.dir/DependInfo.cmake"
  "libs/alignment/CMakeFiles/indi_Nearest_MathPlugin.dir/DependInfo.cmake"
  "libs/alignment/CMakeFiles/indi_SVD_MathPlugin.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indilx200.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_lx200basic.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_lx200_TeenAstro.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_lx200generic.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_celestron_gps.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_rainbow_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_crux_mount.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_temma_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_paramount_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_astrotrac_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_synscan_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_synscanlegacy_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_skycommander_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_dsc_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_ieqlegacy_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_ieq_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_ioptronv3_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_pmc8_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_simulator_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_script_telescope.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_skywatcherAltAzMount.dir/DependInfo.cmake"
  "drivers/telescope/CMakeFiles/indi_planewave_telescope.dir/DependInfo.cmake"
  "drivers/ccd/CMakeFiles/indi_simulator_ccd.dir/DependInfo.cmake"
  "drivers/ccd/CMakeFiles/indi_simulator_guide.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_simulator_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_robo_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_ieaf_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_fcusb_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_nfocus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_nstep_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_efa_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_celestron_sct_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_aaf2_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_rbfocus_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_astromechfoc.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_pinefeat_cef_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_moonlite_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_moonlitedro_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_myfocuserpro2_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_onfocus_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_teenastro_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_sestosenso_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_sestosenso2_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_esatto_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_esattoarco_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_rainbowrsf_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_dreamfocuser_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_lakeside_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_dmfc_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_pegasus_focuscube.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_pegasus_focuscube3.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_pegasus_prodigyMF.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_pegasus_scopsoag.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_usbfocusv3_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_microtouch_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_steeldrive2_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_steeldrive_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_alluna_tcs2.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_lynx_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_perfectstar_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_siefs_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_hitecastrodc_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_deepskydad_af1_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_deepskydad_af2_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_deepskydad_af3_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_smartfocus_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_tcfs_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_lacerta_mfoc_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_lacerta_mfoc_fmc_focus.dir/DependInfo.cmake"
  "drivers/focuser/CMakeFiles/indi_activefocuser_focus.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_nframe_rotator.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_simulator_rotator.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_gemini_focus.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_nightcrawler_focus.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_pyxis_rotator.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_falcon_rotator.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_camelot_rotator.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_falconv2_rotator.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_wanderer_lite_rotator.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_wanderer_rotator_mini.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_wanderer_rotator_lite_v2.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_integra_focus.dir/DependInfo.cmake"
  "drivers/rotator/CMakeFiles/indi_deepskydad_fr1.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_xagyl_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_simulator_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_manual_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_optec_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_quantum_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_trutech_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_qhycfw1_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_qhycfw2_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_qhycfw3_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_pegasusindigo_wheel.dir/DependInfo.cmake"
  "drivers/filter_wheel/CMakeFiles/indi_ioptron_wheel.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_simulator_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_rolloff_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_baader_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_domepro2_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_rigel_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_scopedome_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_ddw_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_script_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_nexdome_beaver.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_dragonlair_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_universalror_dome.dir/DependInfo.cmake"
  "drivers/dome/CMakeFiles/indi_alpaca_dome.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_terrans_powerboxgo_v2.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_terrans_powerboxpro_v2.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_wanderercover_v4_ec.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_astrolink4.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_wandererbox_plus_v3.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_wandererbox_pro_v3.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_wanderer_cover.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_skysafari.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_watchdog.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_flipflat.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_simulator_lightpanel.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_simulator_dustcover.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_pegasus_upb.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_pegasus_uch.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_pegasus_flatmaster.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_pegasus_ppb.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_pegasus_ppba.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_pegasus_spb.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_Excalibur.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_snapcap.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_astromech_lpm.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_astrolink4mini2.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_sqm_weather.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_simulator_sqm.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_astrometry.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_star2000.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_arduinost4.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_planewave_deltat.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_gpusb.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_joystick.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_simulator_gps.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_usbdewpoint.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_myDewControllerPro.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_cheapodc.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_mydcp4esp32.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_deepskydad_fp.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_giotto.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_alto.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_dragon_light.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_gemini_flatpanel.dir/DependInfo.cmake"
  "drivers/auxiliary/CMakeFiles/indi_ups_safety.dir/DependInfo.cmake"
  "drivers/io/CMakeFiles/indi_wavesharemodbus_relay.dir/DependInfo.cmake"
  "drivers/io/CMakeFiles/indi_ipx800v4.dir/DependInfo.cmake"
  "drivers/io/CMakeFiles/indi_simulator_io.dir/DependInfo.cmake"
  "drivers/receiver/CMakeFiles/indi_simulator_receiver.dir/DependInfo.cmake"
  "drivers/receiver/CMakeFiles/indi_rtlsdr.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_simulator_weather.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_meta_weather.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_watcher_weather.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_aagsolo_weather.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_weather_safety_proxy.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_mbox_weather.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_uranus_weather.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_vantage_weather.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_openweathermap_weather.dir/DependInfo.cmake"
  "drivers/weather/CMakeFiles/indi_weather_safety_alpaca.dir/DependInfo.cmake"
  "drivers/agent/CMakeFiles/indi_imager_agent.dir/DependInfo.cmake"
  "drivers/video/CMakeFiles/indi_v4l2_ccd.dir/DependInfo.cmake"
  "drivers/spectrograph/CMakeFiles/shelyak_usis.dir/DependInfo.cmake"
  "drivers/spectrograph/CMakeFiles/indi_spectracyber.dir/DependInfo.cmake"
  "tools/CMakeFiles/indi_getprop.dir/DependInfo.cmake"
  "tools/CMakeFiles/indi_setprop.dir/DependInfo.cmake"
  "tools/CMakeFiles/indi_eval.dir/DependInfo.cmake"
  "examples/tutorial_one/CMakeFiles/tutorial_one.dir/DependInfo.cmake"
  "examples/tutorial_two/CMakeFiles/tutorial_two.dir/DependInfo.cmake"
  "examples/tutorial_three/CMakeFiles/tutorial_three.dir/DependInfo.cmake"
  "examples/tutorial_four/CMakeFiles/tutorial_four.dir/DependInfo.cmake"
  "examples/tutorial_five/CMakeFiles/tutorial_dome.dir/DependInfo.cmake"
  "examples/tutorial_five/CMakeFiles/tutorial_rain.dir/DependInfo.cmake"
  "examples/tutorial_six/CMakeFiles/tutorial_client.dir/DependInfo.cmake"
  "examples/tutorial_seven/CMakeFiles/tutorial_seven.dir/DependInfo.cmake"
  "examples/tutorial_eight/CMakeFiles/tutorial_eight.dir/DependInfo.cmake"
  )
