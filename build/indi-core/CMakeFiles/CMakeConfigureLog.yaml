
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (PROJECT)"
    message: |
      The system is: Linux - 5.15.0-139-generic - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (PROJECT)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is GNU, found in:
        /home/<USER>/Projects/build/indi-core/CMakeFiles/3.29.0/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (PROJECT)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is GNU, found in:
        /home/<USER>/Projects/build/indi-core/CMakeFiles/3.29.0/CompilerIdCXX/a.out
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:64 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (PROJECT)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-8yWFoI"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-8yWFoI"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-8yWFoI'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b2cbf/fast
        /usr/bin/make  -f CMakeFiles/cmTC_b2cbf.dir/build.make CMakeFiles/cmTC_b2cbf.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-8yWFoI'
        Building C object CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -v -o CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake-3.29/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        OFFLOAD_TARGET_NAMES=nvptx-none:hsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
        Thread model: posix
        gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64'
         /usr/lib/gcc/x86_64-linux-gnu/9/cc1 -quiet -v -imultiarch x86_64-linux-gnu /usr/local/share/cmake-3.29/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/cc80t6no.s
        GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/lib/gcc/x86_64-linux-gnu/9/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 01da938ff5dc2163489aa33cb3b747a7
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64'
         as -v --64 -o CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o /tmp/cc80t6no.s
        GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64'
        Linking C executable cmTC_b2cbf
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b2cbf.dir/link.txt --verbose=1
        /usr/bin/cc  -v -Wl,-v CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -o cmTC_b2cbf
        Using built-in specs.
        COLLECT_GCC=/usr/bin/cc
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper
        OFFLOAD_TARGET_NAMES=nvptx-none:hsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
        Thread model: posix
        gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) 
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_b2cbf' '-mtune=generic' '-march=x86-64'
         /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccqOD08P.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_b2cbf /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. -v CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o
        collect2 version 9.4.0
        /usr/bin/ld -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccqOD08P.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_b2cbf /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. -v CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o
        GNU ld (GNU Binutils for Ubuntu) 2.34
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_b2cbf' '-mtune=generic' '-march=x86-64'
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-8yWFoI'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:134 (message)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (PROJECT)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/lib/gcc/x86_64-linux-gnu/9/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/9/include] ==> [/usr/lib/gcc/x86_64-linux-gnu/9/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/lib/gcc/x86_64-linux-gnu/9/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:170 (message)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (PROJECT)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-8yWFoI']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b2cbf/fast]
        ignore line: [/usr/bin/make  -f CMakeFiles/cmTC_b2cbf.dir/build.make CMakeFiles/cmTC_b2cbf.dir/build]
        ignore line: [make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-8yWFoI']
        ignore line: [Building C object CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -v -o CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake-3.29/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/cc1 -quiet -v -imultiarch x86_64-linux-gnu /usr/local/share/cmake-3.29/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/cc80t6no.s]
        ignore line: [GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C17 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 01da938ff5dc2163489aa33cb3b747a7]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ as -v --64 -o CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o /tmp/cc80t6no.s]
        ignore line: [GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [Linking C executable cmTC_b2cbf]
        ignore line: [/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b2cbf.dir/link.txt --verbose=1]
        ignore line: [/usr/bin/cc  -v -Wl -v CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -o cmTC_b2cbf]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/cc]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_b2cbf' '-mtune=generic' '-march=x86-64']
        link line: [ /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccqOD08P.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_b2cbf /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. -v CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccqOD08P.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-pie] ==> ignore
          arg [-znow] ==> ignore
          arg [-zrelro] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_b2cbf] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [-lc] ==> lib [c]
          arg [-lgcc] ==> lib [gcc]
          arg [--push-state] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [--pop-state] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
        ignore line: [collect2 version 9.4.0]
        ignore line: [/usr/bin/ld -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccqOD08P.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_b2cbf /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. -v CMakeFiles/cmTC_b2cbf.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
        linker tool for 'C': /usr/bin/ld
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> [/usr/lib/x86_64-linux-gnu/Scrt1.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> [/usr/lib/x86_64-linux-gnu/crti.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> [/usr/lib/x86_64-linux-gnu/crtn.o]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/usr/lib/gcc/x86_64-linux-gnu/9]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> [/usr/lib]
        implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
        implicit objs: [/usr/lib/x86_64-linux-gnu/Scrt1.o;/usr/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o;/usr/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/9;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:207 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (PROJECT)"
    message: |
      Running the C compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils for Ubuntu) 2.34
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:64 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (PROJECT)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-dCKRRL"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-dCKRRL"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-dCKRRL'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_06ea6/fast
        /usr/bin/make  -f CMakeFiles/cmTC_06ea6.dir/build.make CMakeFiles/cmTC_06ea6.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-dCKRRL'
        Building CXX object CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -v -o CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -c /usr/local/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        OFFLOAD_TARGET_NAMES=nvptx-none:hsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
        Thread model: posix
        gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
         /usr/lib/gcc/x86_64-linux-gnu/9/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/local/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccWRKmtE.s
        GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/9"
        ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"
        ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/include/c++/9
         /usr/include/x86_64-linux-gnu/c++/9
         /usr/include/c++/9/backward
         /usr/lib/gcc/x86_64-linux-gnu/9/include
         /usr/local/include
         /usr/include/x86_64-linux-gnu
         /usr/include
        End of search list.
        GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)
        	compiled by GNU C version 9.4.0, GMP version 6.2.0, MPFR version 4.0.2, MPC version 1.1.0, isl version isl-0.22.1-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 3d1eba838554fa2348dba760e4770469
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
         as -v --64 -o CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccWRKmtE.s
        GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
        Linking CXX executable cmTC_06ea6
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_06ea6.dir/link.txt --verbose=1
        /usr/bin/c++  -v -Wl,-v CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_06ea6
        Using built-in specs.
        COLLECT_GCC=/usr/bin/c++
        COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper
        OFFLOAD_TARGET_NAMES=nvptx-none:hsa
        OFFLOAD_TARGET_DEFAULT=1
        Target: x86_64-linux-gnu
        Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c,ada,c++,go,brig,d,fortran,objc,obj-c++,gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32,m64,mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr,hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu
        Thread model: posix
        gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) 
        COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/
        LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_06ea6' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
         /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccrW4ox5.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_06ea6 /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. -v CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o
        collect2 version 9.4.0
        /usr/bin/ld -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccrW4ox5.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_06ea6 /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. -v CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o
        GNU ld (GNU Binutils for Ubuntu) 2.34
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_06ea6' '-shared-libgcc' '-mtune=generic' '-march=x86-64'
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-dCKRRL'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:134 (message)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (PROJECT)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/include/c++/9]
          add: [/usr/include/x86_64-linux-gnu/c++/9]
          add: [/usr/include/c++/9/backward]
          add: [/usr/lib/gcc/x86_64-linux-gnu/9/include]
          add: [/usr/local/include]
          add: [/usr/include/x86_64-linux-gnu]
          add: [/usr/include]
        end of search list found
        collapse include dir [/usr/include/c++/9] ==> [/usr/include/c++/9]
        collapse include dir [/usr/include/x86_64-linux-gnu/c++/9] ==> [/usr/include/x86_64-linux-gnu/c++/9]
        collapse include dir [/usr/include/c++/9/backward] ==> [/usr/include/c++/9/backward]
        collapse include dir [/usr/lib/gcc/x86_64-linux-gnu/9/include] ==> [/usr/lib/gcc/x86_64-linux-gnu/9/include]
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/usr/include/x86_64-linux-gnu] ==> [/usr/include/x86_64-linux-gnu]
        collapse include dir [/usr/include] ==> [/usr/include]
        implicit include dirs: [/usr/include/c++/9;/usr/include/x86_64-linux-gnu/c++/9;/usr/include/c++/9/backward;/usr/lib/gcc/x86_64-linux-gnu/9/include;/usr/local/include;/usr/include/x86_64-linux-gnu;/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:170 (message)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (PROJECT)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-dCKRRL']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_06ea6/fast]
        ignore line: [/usr/bin/make  -f CMakeFiles/cmTC_06ea6.dir/build.make CMakeFiles/cmTC_06ea6.dir/build]
        ignore line: [make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-dCKRRL']
        ignore line: [Building CXX object CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -v -o CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -c /usr/local/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/cc1plus -quiet -v -imultiarch x86_64-linux-gnu -D_GNU_SOURCE /usr/local/share/cmake-3.29/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -version -fasynchronous-unwind-tables -fstack-protector-strong -Wformat -Wformat-security -fstack-clash-protection -fcf-protection -o /tmp/ccWRKmtE.s]
        ignore line: [GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/usr/include/x86_64-linux-gnu/c++/9"]
        ignore line: [ignoring nonexistent directory "/usr/local/include/x86_64-linux-gnu"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/include-fixed"]
        ignore line: [ignoring nonexistent directory "/usr/lib/gcc/x86_64-linux-gnu/9/../../../../x86_64-linux-gnu/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/include/c++/9]
        ignore line: [ /usr/include/x86_64-linux-gnu/c++/9]
        ignore line: [ /usr/include/c++/9/backward]
        ignore line: [ /usr/lib/gcc/x86_64-linux-gnu/9/include]
        ignore line: [ /usr/local/include]
        ignore line: [ /usr/include/x86_64-linux-gnu]
        ignore line: [ /usr/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++14 (Ubuntu 9.4.0-1ubuntu1~20.04.2) version 9.4.0 (x86_64-linux-gnu)]
        ignore line: [	compiled by GNU C version 9.4.0  GMP version 6.2.0  MPFR version 4.0.2  MPC version 1.1.0  isl version isl-0.22.1-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 3d1eba838554fa2348dba760e4770469]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
        ignore line: [ as -v --64 -o CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccWRKmtE.s]
        ignore line: [GNU assembler version 2.34 (x86_64-linux-gnu) using BFD version (GNU Binutils for Ubuntu) 2.34]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
        ignore line: [Linking CXX executable cmTC_06ea6]
        ignore line: [/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_06ea6.dir/link.txt --verbose=1]
        ignore line: [/usr/bin/c++  -v -Wl -v CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_06ea6]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/usr/bin/c++]
        ignore line: [COLLECT_LTO_WRAPPER=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper]
        ignore line: [OFFLOAD_TARGET_NAMES=nvptx-none:hsa]
        ignore line: [OFFLOAD_TARGET_DEFAULT=1]
        ignore line: [Target: x86_64-linux-gnu]
        ignore line: [Configured with: ../src/configure -v --with-pkgversion='Ubuntu 9.4.0-1ubuntu1~20.04.2' --with-bugurl=file:///usr/share/doc/gcc-9/README.Bugs --enable-languages=c ada c++ go brig d fortran objc obj-c++ gm2 --prefix=/usr --with-gcc-major-version-only --program-suffix=-9 --program-prefix=x86_64-linux-gnu- --enable-shared --enable-linker-build-id --libexecdir=/usr/lib --without-included-gettext --enable-threads=posix --libdir=/usr/lib --enable-nls --enable-clocale=gnu --enable-libstdcxx-debug --enable-libstdcxx-time=yes --with-default-libstdcxx-abi=new --enable-gnu-unique-object --disable-vtable-verify --enable-plugin --enable-default-pie --with-system-zlib --with-target-system-zlib=auto --enable-objc-gc=auto --enable-multiarch --disable-werror --with-arch-32=i686 --with-abi=m64 --with-multilib-list=m32 m64 mx32 --enable-multilib --with-tune=generic --enable-offload-targets=nvptx-none=/build/gcc-9-9QDOt0/gcc-9-9.4.0/debian/tmp-nvptx/usr hsa --without-cuda-driver --enable-checking=release --build=x86_64-linux-gnu --host=x86_64-linux-gnu --target=x86_64-linux-gnu]
        ignore line: [Thread model: posix]
        ignore line: [gcc version 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.2) ]
        ignore line: [COMPILER_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/]
        ignore line: [LIBRARY_PATH=/usr/lib/gcc/x86_64-linux-gnu/9/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/9/../../../:/lib/:/usr/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_06ea6' '-shared-libgcc' '-mtune=generic' '-march=x86-64']
        link line: [ /usr/lib/gcc/x86_64-linux-gnu/9/collect2 -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccrW4ox5.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_06ea6 /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. -v CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/collect2] ==> ignore
          arg [-plugin] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so] ==> ignore
          arg [-plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper] ==> ignore
          arg [-plugin-opt=-fresolution=/tmp/ccrW4ox5.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [--build-id] ==> ignore
          arg [--eh-frame-hdr] ==> ignore
          arg [-m] ==> ignore
          arg [elf_x86_64] ==> ignore
          arg [--hash-style=gnu] ==> ignore
          arg [--as-needed] ==> ignore
          arg [-dynamic-linker] ==> ignore
          arg [/lib64/ld-linux-x86-64.so.2] ==> ignore
          arg [-pie] ==> ignore
          arg [-znow] ==> ignore
          arg [-zrelro] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_06ea6] ==> ignore
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib]
          arg [-L/lib/x86_64-linux-gnu] ==> dir [/lib/x86_64-linux-gnu]
          arg [-L/lib/../lib] ==> dir [/lib/../lib]
          arg [-L/usr/lib/x86_64-linux-gnu] ==> dir [/usr/lib/x86_64-linux-gnu]
          arg [-L/usr/lib/../lib] ==> dir [/usr/lib/../lib]
          arg [-L/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..]
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lm] ==> lib [m]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lc] ==> lib [c]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o]
          arg [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
        ignore line: [collect2 version 9.4.0]
        ignore line: [/usr/bin/ld -plugin /usr/lib/gcc/x86_64-linux-gnu/9/liblto_plugin.so -plugin-opt=/usr/lib/gcc/x86_64-linux-gnu/9/lto-wrapper -plugin-opt=-fresolution=/tmp/ccrW4ox5.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro -o cmTC_06ea6 /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/9 -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/9/../../.. -v CMakeFiles/cmTC_06ea6.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o]
        linker tool for 'CXX': /usr/bin/ld
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/Scrt1.o] ==> [/usr/lib/x86_64-linux-gnu/Scrt1.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crti.o] ==> [/usr/lib/x86_64-linux-gnu/crti.o]
        collapse obj [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu/crtn.o] ==> [/usr/lib/x86_64-linux-gnu/crtn.o]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9] ==> [/usr/lib/gcc/x86_64-linux-gnu/9]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../../../lib] ==> [/usr/lib]
        collapse library dir [/lib/x86_64-linux-gnu] ==> [/lib/x86_64-linux-gnu]
        collapse library dir [/lib/../lib] ==> [/lib]
        collapse library dir [/usr/lib/x86_64-linux-gnu] ==> [/usr/lib/x86_64-linux-gnu]
        collapse library dir [/usr/lib/../lib] ==> [/usr/lib]
        collapse library dir [/usr/lib/gcc/x86_64-linux-gnu/9/../../..] ==> [/usr/lib]
        implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
        implicit objs: [/usr/lib/x86_64-linux-gnu/Scrt1.o;/usr/lib/x86_64-linux-gnu/crti.o;/usr/lib/gcc/x86_64-linux-gnu/9/crtbeginS.o;/usr/lib/gcc/x86_64-linux-gnu/9/crtendS.o;/usr/lib/x86_64-linux-gnu/crtn.o]
        implicit dirs: [/usr/lib/gcc/x86_64-linux-gnu/9;/usr/lib/x86_64-linux-gnu;/usr/lib;/lib/x86_64-linux-gnu;/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeDetermineCompilerABI.cmake:207 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake-3.29/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (PROJECT)"
    message: |
      Running the CXX compiler's linker: "/usr/bin/ld" "-v"
      GNU ld (GNU Binutils for Ubuntu) 2.34
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake-3.29/Modules/CheckCCompilerFlag.cmake:51 (cmake_check_compiler_flag)"
      - "cmake_modules/CMakeCommon.cmake:35 (CHECK_C_COMPILER_FLAG)"
      - "CMakeLists.txt:19 (include)"
    checks:
      - "Performing Test COMPATIBLE_FORTIFY_SOURCE"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-7a7aDJ"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-7a7aDJ"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/home/<USER>/Projects/indi/cmake_modules/;/home/<USER>/Projects/indi/../cmake_modules/"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "COMPATIBLE_FORTIFY_SOURCE"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-7a7aDJ'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_dbd6d/fast
        /usr/bin/make  -f CMakeFiles/cmTC_dbd6d.dir/build.make CMakeFiles/cmTC_dbd6d.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-7a7aDJ'
        Building C object CMakeFiles/cmTC_dbd6d.dir/src.c.o
        /usr/bin/cc -DCOMPATIBLE_FORTIFY_SOURCE  -fPIE   -Werror -D_FORTIFY_SOURCE=2 -o CMakeFiles/cmTC_dbd6d.dir/src.c.o -c /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-7a7aDJ/src.c
        Linking C executable cmTC_dbd6d
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_dbd6d.dir/link.txt --verbose=1
        /usr/bin/cc CMakeFiles/cmTC_dbd6d.dir/src.c.o -o cmTC_dbd6d
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-7a7aDJ'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CheckSymbolExists.cmake:140 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/CheckSymbolExists.cmake:66 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "CMakeLists.txt:113 (check_symbol_exists)"
    checks:
      - "Looking for mremap"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-YbnuoM"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-YbnuoM"
    cmakeVariables:
      CMAKE_C_FLAGS: " -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "  -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now "
      CMAKE_MODULE_PATH: "/home/<USER>/Projects/indi/cmake_modules/;/home/<USER>/Projects/indi/../cmake_modules/"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_MREMAP"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-YbnuoM'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_a3838/fast
        /usr/bin/make  -f CMakeFiles/cmTC_a3838.dir/build.make CMakeFiles/cmTC_a3838.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-YbnuoM'
        Building C object CMakeFiles/cmTC_a3838.dir/CheckSymbolExists.c.o
        /usr/bin/cc -D_GNU_SOURCE  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g  -fPIE -o CMakeFiles/cmTC_a3838.dir/CheckSymbolExists.c.o -c /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-YbnuoM/CheckSymbolExists.c
        Linking C executable cmTC_a3838
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_a3838.dir/link.txt --verbose=1
        /usr/bin/cc  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g    -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now   CMakeFiles/cmTC_a3838.dir/CheckSymbolExists.c.o -o cmTC_a3838
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-YbnuoM'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CheckSymbolExists.cmake:140 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/CheckSymbolExists.cmake:66 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "libs/indicore/CMakeLists.txt:12 (check_symbol_exists)"
    checks:
      - "Looking for timespec_get"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-irSluJ"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-irSluJ"
    cmakeVariables:
      CMAKE_C_FLAGS: " -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "  -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now "
      CMAKE_MODULE_PATH: "/home/<USER>/Projects/indi/cmake_modules/;/home/<USER>/Projects/indi/../cmake_modules/"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_TIMESPEC_GET"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-irSluJ'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_749ed/fast
        /usr/bin/make  -f CMakeFiles/cmTC_749ed.dir/build.make CMakeFiles/cmTC_749ed.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-irSluJ'
        Building C object CMakeFiles/cmTC_749ed.dir/CheckSymbolExists.c.o
        /usr/bin/cc -D_GNU_SOURCE  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP  -fPIE -o CMakeFiles/cmTC_749ed.dir/CheckSymbolExists.c.o -c /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-irSluJ/CheckSymbolExists.c
        Linking C executable cmTC_749ed
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_749ed.dir/link.txt --verbose=1
        /usr/bin/cc  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP    -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now   CMakeFiles/cmTC_749ed.dir/CheckSymbolExists.c.o -o cmTC_749ed  /usr/lib/x86_64-linux-gnu/libnova.so
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-irSluJ'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CheckSymbolExists.cmake:140 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/CheckSymbolExists.cmake:66 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "libs/indicore/CMakeLists.txt:13 (check_symbol_exists)"
    checks:
      - "Looking for clock_gettime"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-hGwCfM"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-hGwCfM"
    cmakeVariables:
      CMAKE_C_FLAGS: " -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "  -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now "
      CMAKE_MODULE_PATH: "/home/<USER>/Projects/indi/cmake_modules/;/home/<USER>/Projects/indi/../cmake_modules/"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "HAVE_CLOCK_GETTIME"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-hGwCfM'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_ea97d/fast
        /usr/bin/make  -f CMakeFiles/cmTC_ea97d.dir/build.make CMakeFiles/cmTC_ea97d.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-hGwCfM'
        Building C object CMakeFiles/cmTC_ea97d.dir/CheckSymbolExists.c.o
        /usr/bin/cc -D_GNU_SOURCE  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP  -fPIE -o CMakeFiles/cmTC_ea97d.dir/CheckSymbolExists.c.o -c /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-hGwCfM/CheckSymbolExists.c
        Linking C executable cmTC_ea97d
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_ea97d.dir/link.txt --verbose=1
        /usr/bin/cc  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP    -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now   CMakeFiles/cmTC_ea97d.dir/CheckSymbolExists.c.o -o cmTC_ea97d  /usr/lib/x86_64-linux-gnu/libnova.so
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-hGwCfM'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake-3.29/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "/usr/local/share/cmake-3.29/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "indiserver/CMakeLists.txt:7 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK"
    cmakeVariables:
      CMAKE_C_FLAGS: " -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "  -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now "
      CMAKE_MODULE_PATH: "/home/<USER>/Projects/indi/cmake_modules/;/home/<USER>/Projects/indi/../cmake_modules/"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_1afd5/fast
        /usr/bin/make  -f CMakeFiles/cmTC_1afd5.dir/build.make CMakeFiles/cmTC_1afd5.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK'
        Building C object CMakeFiles/cmTC_1afd5.dir/src.c.o
        /usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD -D_GNU_SOURCE  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP  -fPIE -o CMakeFiles/cmTC_1afd5.dir/src.c.o -c /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK/src.c
        Linking C executable cmTC_1afd5
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_1afd5.dir/link.txt --verbose=1
        /usr/bin/cc  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP    -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now   CMakeFiles/cmTC_1afd5.dir/src.c.o -o cmTC_1afd5
        /usr/bin/ld: CMakeFiles/cmTC_1afd5.dir/src.c.o: in function `main':
        /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK/src.c:11: undefined reference to `pthread_create'
        /usr/bin/ld: /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK/src.c:12: undefined reference to `pthread_detach'
        /usr/bin/ld: /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK/src.c:13: undefined reference to `pthread_cancel'
        /usr/bin/ld: /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK/src.c:14: undefined reference to `pthread_join'
        collect2: error: ld returned 1 exit status
        make[1]: *** [CMakeFiles/cmTC_1afd5.dir/build.make:99: cmTC_1afd5] Error 1
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-tCvOqK'
        make: *** [Makefile:127: cmTC_1afd5/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "/usr/local/share/cmake-3.29/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "indiserver/CMakeLists.txt:7 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-6MiK5H"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-6MiK5H"
    cmakeVariables:
      CMAKE_C_FLAGS: " -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "  -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now "
      CMAKE_MODULE_PATH: "/home/<USER>/Projects/indi/cmake_modules/;/home/<USER>/Projects/indi/../cmake_modules/"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-6MiK5H'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_79f84/fast
        /usr/bin/make  -f CMakeFiles/cmTC_79f84.dir/build.make CMakeFiles/cmTC_79f84.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-6MiK5H'
        Building C object CMakeFiles/cmTC_79f84.dir/CheckFunctionExists.c.o
        /usr/bin/cc -D_GNU_SOURCE  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP -DCHECK_FUNCTION_EXISTS=pthread_create -fPIE -o CMakeFiles/cmTC_79f84.dir/CheckFunctionExists.c.o -c /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-6MiK5H/CheckFunctionExists.c
        Linking C executable cmTC_79f84
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_79f84.dir/link.txt --verbose=1
        /usr/bin/cc  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP -DCHECK_FUNCTION_EXISTS=pthread_create   -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now   CMakeFiles/cmTC_79f84.dir/CheckFunctionExists.c.o -o cmTC_79f84  -lpthreads
        /usr/bin/ld: cannot find -lpthreads
        collect2: error: ld returned 1 exit status
        make[1]: *** [CMakeFiles/cmTC_79f84.dir/build.make:99: cmTC_79f84] Error 1
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-6MiK5H'
        make: *** [Makefile:127: cmTC_79f84/fast] Error 2
        
      exitCode: 2
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "/usr/local/share/cmake-3.29/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "indiserver/CMakeLists.txt:7 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-XHKB7J"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-XHKB7J"
    cmakeVariables:
      CMAKE_C_FLAGS: " -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "  -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now "
      CMAKE_MODULE_PATH: "/home/<USER>/Projects/indi/cmake_modules/;/home/<USER>/Projects/indi/../cmake_modules/"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-XHKB7J'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_56791/fast
        /usr/bin/make  -f CMakeFiles/cmTC_56791.dir/build.make CMakeFiles/cmTC_56791.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-XHKB7J'
        Building C object CMakeFiles/cmTC_56791.dir/CheckFunctionExists.c.o
        /usr/bin/cc -D_GNU_SOURCE  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP -DCHECK_FUNCTION_EXISTS=pthread_create -fPIE -o CMakeFiles/cmTC_56791.dir/CheckFunctionExists.c.o -c /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-XHKB7J/CheckFunctionExists.c
        Linking C executable cmTC_56791
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_56791.dir/link.txt --verbose=1
        /usr/bin/cc  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP -DCHECK_FUNCTION_EXISTS=pthread_create   -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now   CMakeFiles/cmTC_56791.dir/CheckFunctionExists.c.o -o cmTC_56791  -lpthread
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-XHKB7J'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "cmake_modules/FindUSB1.cmake:80 (check_cxx_source_compiles)"
      - "CMakeLists.txt:283 (find_package)"
    checks:
      - "Performing Test USB1_HAS_LIBUSB_ERROR_NAME"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-FwICrI"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-FwICrI"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "  -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now "
      CMAKE_MODULE_PATH: "/home/<USER>/Projects/indi/cmake_modules/;/home/<USER>/Projects/indi/../cmake_modules/"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "USB1_HAS_LIBUSB_ERROR_NAME"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-FwICrI'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_121da/fast
        /usr/bin/make  -f CMakeFiles/cmTC_121da.dir/build.make CMakeFiles/cmTC_121da.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-FwICrI'
        Building CXX object CMakeFiles/cmTC_121da.dir/src.cxx.o
        /usr/bin/c++ -DUSB1_HAS_LIBUSB_ERROR_NAME -I/usr/include/libusb-1.0 -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP  -std=gnu++17 -fPIE -o CMakeFiles/cmTC_121da.dir/src.cxx.o -c /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-FwICrI/src.cxx
        Linking CXX executable cmTC_121da
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_121da.dir/link.txt --verbose=1
        /usr/bin/c++  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP    -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now   CMakeFiles/cmTC_121da.dir/src.cxx.o -o cmTC_121da  /usr/lib/x86_64-linux-gnu/libusb-1.0.so
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-FwICrI'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake-3.29/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "/usr/local/share/cmake-3.29/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake-3.29/Modules/FindIconv.cmake:115 (check_c_source_compiles)"
      - "CMakeLists.txt:289 (find_package)"
    checks:
      - "Performing Test Iconv_IS_BUILT_IN"
    directories:
      source: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-Hq3b6K"
      binary: "/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-Hq3b6K"
    cmakeVariables:
      CMAKE_C_FLAGS: " -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "  -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now "
      CMAKE_MODULE_PATH: "/home/<USER>/Projects/indi/cmake_modules/;/home/<USER>/Projects/indi/../cmake_modules/"
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "Iconv_IS_BUILT_IN"
      cached: true
      stdout: |
        Change Dir: '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-Hq3b6K'
        
        Run Build Command(s): /usr/local/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_68393/fast
        /usr/bin/make  -f CMakeFiles/cmTC_68393.dir/build.make CMakeFiles/cmTC_68393.dir/build
        make[1]: Entering directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-Hq3b6K'
        Building C object CMakeFiles/cmTC_68393.dir/src.c.o
        /usr/bin/cc -DIconv_IS_BUILT_IN  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP  -fPIE -o CMakeFiles/cmTC_68393.dir/src.c.o -c /home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-Hq3b6K/src.c
        Linking C executable cmTC_68393
        /usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_68393.dir/link.txt --verbose=1
        /usr/bin/cc  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP    -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now   CMakeFiles/cmTC_68393.dir/src.c.o -o cmTC_68393
        make[1]: Leaving directory '/home/<USER>/Projects/build/indi-core/CMakeFiles/CMakeScratch/TryCompile-Hq3b6K'
        
      exitCode: 0
...
