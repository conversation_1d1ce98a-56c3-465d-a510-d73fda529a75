/usr/bin/c++  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP -g   -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,rel<PERSON> -Wl,-z,now  CMakeFiles/indi_gpusb.dir/gpdriver.cpp.o CMakeFiles/indi_gpusb.dir/gpusb.cpp.o -o indi_gpusb  -Wl,-rpath,/home/<USER>/Projects/build/indi-core/libs/indibase: ../../libs/indibase/libindidriver.so.2.1.4 -lrt /usr/lib/x86_64-linux-gnu/libusb-1.0.so /usr/lib/x86_64-linux-gnu/libnova.so -lpthread /usr/lib/x86_64-linux-gnu/libcfitsio.so /usr/lib/x86_64-linux-gnu/libz.so /usr/lib/x86_64-linux-gnu/libjpeg.so /usr/lib/x86_64-linux-gnu/libfftw3.so -lm /usr/lib/x86_64-linux-gnu/libcurl.so /usr/lib/x86_64-linux-gnu/libtheoraenc.so /usr/lib/x86_64-linux-gnu/libtheoradec.so /usr/lib/x86_64-linux-gnu/libogg.so
