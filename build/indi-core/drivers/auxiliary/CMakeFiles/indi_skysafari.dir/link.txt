/usr/bin/c++  -D_FORTIFY_SOURCE=2 -O1 -Wa,--noexecstack  -Wall -Wextra -Wno-format-truncation -g -DHAVE_MREMAP -g   -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,rel<PERSON> -<PERSON>l,-z,now  CMakeFiles/indi_skysafari.dir/skysafari.cpp.o CMakeFiles/indi_skysafari.dir/skysafariclient.cpp.o -o indi_skysafari  -Wl,-rpath,/home/<USER>/Projects/build/indi-core/libs/indibase:/home/<USER>/Projects/build/indi-core/libs/indiclient: ../../libs/indibase/libindidriver.so.2.1.4 ../../libs/indiclient/libindiclient.so.2.1.4 /usr/lib/x86_64-linux-gnu/libusb-1.0.so /usr/lib/x86_64-linux-gnu/libcfitsio.so /usr/lib/x86_64-linux-gnu/libjpeg.so /usr/lib/x86_64-linux-gnu/libfftw3.so -lm /usr/lib/x86_64-linux-gnu/libcurl.so /usr/lib/x86_64-linux-gnu/libtheoraenc.so /usr/lib/x86_64-linux-gnu/libtheoradec.so /usr/lib/x86_64-linux-gnu/libogg.so /usr/lib/x86_64-linux-gnu/libz.so -lrt /usr/lib/x86_64-linux-gnu/libnova.so -lpthread
