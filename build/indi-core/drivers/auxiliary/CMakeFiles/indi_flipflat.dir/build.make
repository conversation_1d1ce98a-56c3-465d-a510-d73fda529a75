# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/indi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/build/indi-core

# Include any dependencies generated for this target.
include drivers/auxiliary/CMakeFiles/indi_flipflat.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include drivers/auxiliary/CMakeFiles/indi_flipflat.dir/compiler_depend.make

# Include the progress variables for this target.
include drivers/auxiliary/CMakeFiles/indi_flipflat.dir/progress.make

# Include the compile flags for this target's objects.
include drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flags.make

drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flip_flat.cpp.o: drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flags.make
drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flip_flat.cpp.o: /home/<USER>/Projects/indi/drivers/auxiliary/flip_flat.cpp
drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flip_flat.cpp.o: drivers/auxiliary/CMakeFiles/indi_flipflat.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-core/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flip_flat.cpp.o"
	cd /home/<USER>/Projects/build/indi-core/drivers/auxiliary && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flip_flat.cpp.o -MF CMakeFiles/indi_flipflat.dir/flip_flat.cpp.o.d -o CMakeFiles/indi_flipflat.dir/flip_flat.cpp.o -c /home/<USER>/Projects/indi/drivers/auxiliary/flip_flat.cpp

drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flip_flat.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_flipflat.dir/flip_flat.cpp.i"
	cd /home/<USER>/Projects/build/indi-core/drivers/auxiliary && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi/drivers/auxiliary/flip_flat.cpp > CMakeFiles/indi_flipflat.dir/flip_flat.cpp.i

drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flip_flat.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_flipflat.dir/flip_flat.cpp.s"
	cd /home/<USER>/Projects/build/indi-core/drivers/auxiliary && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi/drivers/auxiliary/flip_flat.cpp -o CMakeFiles/indi_flipflat.dir/flip_flat.cpp.s

# Object files for target indi_flipflat
indi_flipflat_OBJECTS = \
"CMakeFiles/indi_flipflat.dir/flip_flat.cpp.o"

# External object files for target indi_flipflat
indi_flipflat_EXTERNAL_OBJECTS =

drivers/auxiliary/indi_flipflat: drivers/auxiliary/CMakeFiles/indi_flipflat.dir/flip_flat.cpp.o
drivers/auxiliary/indi_flipflat: drivers/auxiliary/CMakeFiles/indi_flipflat.dir/build.make
drivers/auxiliary/indi_flipflat: libs/indibase/libindidriver.so.2.1.4
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libusb-1.0.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libnova.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libcfitsio.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libz.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libjpeg.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libfftw3.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libm.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libcurl.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libtheoraenc.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libtheoradec.so
drivers/auxiliary/indi_flipflat: /usr/lib/x86_64-linux-gnu/libogg.so
drivers/auxiliary/indi_flipflat: drivers/auxiliary/CMakeFiles/indi_flipflat.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Projects/build/indi-core/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable indi_flipflat"
	cd /home/<USER>/Projects/build/indi-core/drivers/auxiliary && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/indi_flipflat.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
drivers/auxiliary/CMakeFiles/indi_flipflat.dir/build: drivers/auxiliary/indi_flipflat
.PHONY : drivers/auxiliary/CMakeFiles/indi_flipflat.dir/build

drivers/auxiliary/CMakeFiles/indi_flipflat.dir/clean:
	cd /home/<USER>/Projects/build/indi-core/drivers/auxiliary && $(CMAKE_COMMAND) -P CMakeFiles/indi_flipflat.dir/cmake_clean.cmake
.PHONY : drivers/auxiliary/CMakeFiles/indi_flipflat.dir/clean

drivers/auxiliary/CMakeFiles/indi_flipflat.dir/depend:
	cd /home/<USER>/Projects/build/indi-core && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Projects/indi /home/<USER>/Projects/indi/drivers/auxiliary /home/<USER>/Projects/build/indi-core /home/<USER>/Projects/build/indi-core/drivers/auxiliary /home/<USER>/Projects/build/indi-core/drivers/auxiliary/CMakeFiles/indi_flipflat.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : drivers/auxiliary/CMakeFiles/indi_flipflat.dir/depend

