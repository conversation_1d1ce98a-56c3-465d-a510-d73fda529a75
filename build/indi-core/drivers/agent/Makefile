# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/indi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/build/indi-core

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/local/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/local/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Devel\" \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Projects/build/indi-core && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-core/CMakeFiles /home/<USER>/Projects/build/indi-core/drivers/agent//CMakeFiles/progress.marks
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 drivers/agent/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-core/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 drivers/agent/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 drivers/agent/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 drivers/agent/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Projects/build/indi-core && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
drivers/agent/CMakeFiles/indi_imager_agent.dir/rule:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 drivers/agent/CMakeFiles/indi_imager_agent.dir/rule
.PHONY : drivers/agent/CMakeFiles/indi_imager_agent.dir/rule

# Convenience name for target.
indi_imager_agent: drivers/agent/CMakeFiles/indi_imager_agent.dir/rule
.PHONY : indi_imager_agent

# fast build rule for target.
indi_imager_agent/fast:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f drivers/agent/CMakeFiles/indi_imager_agent.dir/build.make drivers/agent/CMakeFiles/indi_imager_agent.dir/build
.PHONY : indi_imager_agent/fast

agent_imager.o: agent_imager.cpp.o
.PHONY : agent_imager.o

# target to build an object file
agent_imager.cpp.o:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f drivers/agent/CMakeFiles/indi_imager_agent.dir/build.make drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o
.PHONY : agent_imager.cpp.o

agent_imager.i: agent_imager.cpp.i
.PHONY : agent_imager.i

# target to preprocess a source file
agent_imager.cpp.i:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f drivers/agent/CMakeFiles/indi_imager_agent.dir/build.make drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.i
.PHONY : agent_imager.cpp.i

agent_imager.s: agent_imager.cpp.s
.PHONY : agent_imager.s

# target to generate assembly for a file
agent_imager.cpp.s:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f drivers/agent/CMakeFiles/indi_imager_agent.dir/build.make drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.s
.PHONY : agent_imager.cpp.s

group.o: group.cpp.o
.PHONY : group.o

# target to build an object file
group.cpp.o:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f drivers/agent/CMakeFiles/indi_imager_agent.dir/build.make drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.o
.PHONY : group.cpp.o

group.i: group.cpp.i
.PHONY : group.i

# target to preprocess a source file
group.cpp.i:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f drivers/agent/CMakeFiles/indi_imager_agent.dir/build.make drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.i
.PHONY : group.cpp.i

group.s: group.cpp.s
.PHONY : group.s

# target to generate assembly for a file
group.cpp.s:
	cd /home/<USER>/Projects/build/indi-core && $(MAKE) $(MAKESILENT) -f drivers/agent/CMakeFiles/indi_imager_agent.dir/build.make drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.s
.PHONY : group.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... indi_imager_agent"
	@echo "... agent_imager.o"
	@echo "... agent_imager.i"
	@echo "... agent_imager.s"
	@echo "... group.o"
	@echo "... group.i"
	@echo "... group.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Projects/build/indi-core && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

