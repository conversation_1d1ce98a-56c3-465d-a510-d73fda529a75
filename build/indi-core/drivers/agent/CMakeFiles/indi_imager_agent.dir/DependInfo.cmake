
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Projects/indi/drivers/agent/agent_imager.cpp" "drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o" "gcc" "drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o.d"
  "/home/<USER>/Projects/indi/drivers/agent/group.cpp" "drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.o" "gcc" "drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
