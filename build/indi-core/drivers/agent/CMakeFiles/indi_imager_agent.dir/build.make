# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/indi

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/build/indi-core

# Include any dependencies generated for this target.
include drivers/agent/CMakeFiles/indi_imager_agent.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include drivers/agent/CMakeFiles/indi_imager_agent.dir/compiler_depend.make

# Include the progress variables for this target.
include drivers/agent/CMakeFiles/indi_imager_agent.dir/progress.make

# Include the compile flags for this target's objects.
include drivers/agent/CMakeFiles/indi_imager_agent.dir/flags.make

drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o: drivers/agent/CMakeFiles/indi_imager_agent.dir/flags.make
drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o: /home/<USER>/Projects/indi/drivers/agent/agent_imager.cpp
drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o: drivers/agent/CMakeFiles/indi_imager_agent.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-core/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o"
	cd /home/<USER>/Projects/build/indi-core/drivers/agent && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o -MF CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o.d -o CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o -c /home/<USER>/Projects/indi/drivers/agent/agent_imager.cpp

drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.i"
	cd /home/<USER>/Projects/build/indi-core/drivers/agent && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi/drivers/agent/agent_imager.cpp > CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.i

drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.s"
	cd /home/<USER>/Projects/build/indi-core/drivers/agent && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi/drivers/agent/agent_imager.cpp -o CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.s

drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.o: drivers/agent/CMakeFiles/indi_imager_agent.dir/flags.make
drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.o: /home/<USER>/Projects/indi/drivers/agent/group.cpp
drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.o: drivers/agent/CMakeFiles/indi_imager_agent.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-core/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.o"
	cd /home/<USER>/Projects/build/indi-core/drivers/agent && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.o -MF CMakeFiles/indi_imager_agent.dir/group.cpp.o.d -o CMakeFiles/indi_imager_agent.dir/group.cpp.o -c /home/<USER>/Projects/indi/drivers/agent/group.cpp

drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_imager_agent.dir/group.cpp.i"
	cd /home/<USER>/Projects/build/indi-core/drivers/agent && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi/drivers/agent/group.cpp > CMakeFiles/indi_imager_agent.dir/group.cpp.i

drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_imager_agent.dir/group.cpp.s"
	cd /home/<USER>/Projects/build/indi-core/drivers/agent && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi/drivers/agent/group.cpp -o CMakeFiles/indi_imager_agent.dir/group.cpp.s

# Object files for target indi_imager_agent
indi_imager_agent_OBJECTS = \
"CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o" \
"CMakeFiles/indi_imager_agent.dir/group.cpp.o"

# External object files for target indi_imager_agent
indi_imager_agent_EXTERNAL_OBJECTS =

drivers/agent/indi_imager_agent: drivers/agent/CMakeFiles/indi_imager_agent.dir/agent_imager.cpp.o
drivers/agent/indi_imager_agent: drivers/agent/CMakeFiles/indi_imager_agent.dir/group.cpp.o
drivers/agent/indi_imager_agent: drivers/agent/CMakeFiles/indi_imager_agent.dir/build.make
drivers/agent/indi_imager_agent: libs/indibase/libindidriver.so.2.1.4
drivers/agent/indi_imager_agent: libs/indiclient/libindiclient.so.2.1.4
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libusb-1.0.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libcfitsio.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libjpeg.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libfftw3.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libm.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libcurl.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libtheoraenc.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libtheoradec.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libogg.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libz.so
drivers/agent/indi_imager_agent: /usr/lib/x86_64-linux-gnu/libnova.so
drivers/agent/indi_imager_agent: drivers/agent/CMakeFiles/indi_imager_agent.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Projects/build/indi-core/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable indi_imager_agent"
	cd /home/<USER>/Projects/build/indi-core/drivers/agent && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/indi_imager_agent.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
drivers/agent/CMakeFiles/indi_imager_agent.dir/build: drivers/agent/indi_imager_agent
.PHONY : drivers/agent/CMakeFiles/indi_imager_agent.dir/build

drivers/agent/CMakeFiles/indi_imager_agent.dir/clean:
	cd /home/<USER>/Projects/build/indi-core/drivers/agent && $(CMAKE_COMMAND) -P CMakeFiles/indi_imager_agent.dir/cmake_clean.cmake
.PHONY : drivers/agent/CMakeFiles/indi_imager_agent.dir/clean

drivers/agent/CMakeFiles/indi_imager_agent.dir/depend:
	cd /home/<USER>/Projects/build/indi-core && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Projects/indi /home/<USER>/Projects/indi/drivers/agent /home/<USER>/Projects/build/indi-core /home/<USER>/Projects/build/indi-core/drivers/agent /home/<USER>/Projects/build/indi-core/drivers/agent/CMakeFiles/indi_imager_agent.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : drivers/agent/CMakeFiles/indi_imager_agent.dir/depend

