# This is the CMakeCache file.
# For build in directory: /home/<USER>/Projects/build/indi-qhy
# It was generated by CMake: /usr/local/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a file.
BUNDLED_JSONLIB:PATH=/usr/include/libindi

//Path to a program.
CCACHE_FOUND:FILEPATH=CCACHE_FOUND-NOTFOUND

//Enable ccache support
CCACHE_SUPPORT:BOOL=OFF

//Path to a file.
CFITSIO_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
CFITSIO_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libcfitsio.so

//Clang's sanitizer support
CLANG_SANITIZERS:BOOL=OFF

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Debug

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-9

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-9

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/Projects/build/indi-qhy/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib/x86_64-linux-gnu)
CMAKE_INSTALL_LIBDIR:PATH=lib/x86_64-linux-gnu

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=indi_qhy

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Enable strict compilation mode to turn compiler warnings to errors
FIX_WARNINGS:BOOL=OFF

//Path to a library.
GSL_CBLAS_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgslcblas.so

//Path to a library.
GSL_CBLAS_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libgslcblas.so

//Path to a file.
GSL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
GSL_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libgsl.so

//Path to a library.
GSL_LIBRARY_DEBUG:FILEPATH=/usr/lib/x86_64-linux-gnu/libgsl.so

GSL_ROOT_DIR:STRING=/usr

//Data directory for INDI
INDI_DATA_DIR:PATH=/usr/share/indi

//Include directory for INDI
INDI_INCLUDE_DIR:PATH=/usr/include/libindi

//Path to a program.
LDGOLD_FOUND:FILEPATH=/usr/bin/ld.gold

//Enable ld.gold support
LDGOLD_SUPPORT:BOOL=OFF

//Path to a file.
NOVA_INCLUDE_DIR:PATH=/usr/include/libnova

//Path to a library.
NOVA_LIBRARIES:FILEPATH=/usr/lib/x86_64-linux-gnu/libnova.so

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a file.
QHY_INCLUDE_DIR:PATH=/usr/local/include

//Path to a library.
QHY_LIBRARIES:FILEPATH=/usr/local/lib/libqhyccd.so

//Path to a file.
USB1_INCLUDE_DIR:PATH=/usr/include/libusb-1.0

//Path to a library.
USB1_LIBRARY:FILEPATH=/usr/lib/x86_64-linux-gnu/libusb-1.0.so

//Path to a file.
WEBSOCKET_HEADER:PATH=WEBSOCKET_HEADER-NOTFOUND

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/usr/lib/x86_64-linux-gnu/libz.so

//Debug libraries for INDI
_INDI_LIB_DEBUG_align:FILEPATH=_INDI_LIB_DEBUG_align-NOTFOUND

//Debug libraries for INDI
_INDI_LIB_DEBUG_driver:FILEPATH=_INDI_LIB_DEBUG_driver-NOTFOUND

//Release libraries for INDI
_INDI_LIB_RELEASE_align:FILEPATH=/usr/lib/x86_64-linux-gnu/libindiAlignmentDriver.so

//Release libraries for INDI
_INDI_LIB_RELEASE_driver:FILEPATH=/usr/lib/x86_64-linux-gnu/libindidriver.so

//Value Computed by CMake
indi_qhy_BINARY_DIR:STATIC=/home/<USER>/Projects/build/indi-qhy

//Value Computed by CMake
indi_qhy_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
indi_qhy_SOURCE_DIR:STATIC=/home/<USER>/Projects/indi-3rdparty/indi-qhy

//Path to a library.
pkgcfg_lib_GSL_gsl:FILEPATH=/usr/lib/x86_64-linux-gnu/libgsl.so

//Path to a library.
pkgcfg_lib_GSL_gslcblas:FILEPATH=/usr/lib/x86_64-linux-gnu/libgslcblas.so

//Path to a library.
pkgcfg_lib_GSL_m:FILEPATH=/usr/lib/x86_64-linux-gnu/libm.so

//Path to a library.
pkgcfg_lib_PC_LIBUSB1_usb-1.0:FILEPATH=/usr/lib/x86_64-linux-gnu/libusb-1.0.so


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CFITSIO_INCLUDE_DIR
CFITSIO_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CFITSIO_LIBRARIES
CFITSIO_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/Projects/build/indi-qhy
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=29
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/local/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/local/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/local/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/local/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/Projects/indi-3rdparty/indi-qhy
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/local/share/cmake-3.29
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Test COMPATIBLE_FORTIFY_SOURCE
COMPATIBLE_FORTIFY_SOURCE:INTERNAL=1
//Details about finding GSL
FIND_PACKAGE_MESSAGE_DETAILS_GSL:INTERNAL=[/usr/include][/usr/lib/x86_64-linux-gnu/libgsl.so][/usr/lib/x86_64-linux-gnu/libgslcblas.so][v2.5()]
//Details about finding INDI
FIND_PACKAGE_MESSAGE_DETAILS_INDI:INTERNAL=[/usr/lib/x86_64-linux-gnu/libindidriver.so;/usr/lib/x86_64-linux-gnu/libindiAlignmentDriver.so][/usr/include/libindi][v2.1.4()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v0.29.1()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding USB1
FIND_PACKAGE_MESSAGE_DETAILS_USB1:INTERNAL=[/usr/lib/x86_64-linux-gnu/libusb-1.0.so][/usr/include/libusb-1.0][v1.0.23()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[/usr/lib/x86_64-linux-gnu/libz.so][/usr/include][c ][v1.2.11()]
//ADVANCED property for variable: GSL_CBLAS_LIBRARY
GSL_CBLAS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GSL_CBLAS_LIBRARY_DEBUG
GSL_CBLAS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
GSL_CFLAGS:INTERNAL=-I/usr/include
GSL_CFLAGS_I:INTERNAL=
GSL_CFLAGS_OTHER:INTERNAL=
GSL_FOUND:INTERNAL=1
GSL_INCLUDEDIR:INTERNAL=/usr/include
//ADVANCED property for variable: GSL_INCLUDE_DIR
GSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
GSL_INCLUDE_DIRS:INTERNAL=/usr/include
GSL_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lgsl;-lgslcblas;-lm
GSL_LDFLAGS_OTHER:INTERNAL=
GSL_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
GSL_LIBRARIES:INTERNAL=gsl;gslcblas;m
//ADVANCED property for variable: GSL_LIBRARY
GSL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GSL_LIBRARY_DEBUG
GSL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
GSL_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
GSL_LIBS:INTERNAL=
GSL_LIBS_L:INTERNAL=
GSL_LIBS_OTHER:INTERNAL=
GSL_LIBS_PATHS:INTERNAL=
GSL_MODULE_NAME:INTERNAL=gsl
GSL_PREFIX:INTERNAL=/usr
//ADVANCED property for variable: GSL_ROOT_DIR
GSL_ROOT_DIR-ADVANCED:INTERNAL=1
GSL_STATIC_CFLAGS:INTERNAL=-I/usr/include
GSL_STATIC_CFLAGS_I:INTERNAL=
GSL_STATIC_CFLAGS_OTHER:INTERNAL=
GSL_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include
GSL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lgsl;-lgslcblas;-lm
GSL_STATIC_LDFLAGS_OTHER:INTERNAL=
GSL_STATIC_LIBDIR:INTERNAL=
GSL_STATIC_LIBRARIES:INTERNAL=gsl;gslcblas;m
GSL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
GSL_STATIC_LIBS:INTERNAL=
GSL_STATIC_LIBS_L:INTERNAL=
GSL_STATIC_LIBS_OTHER:INTERNAL=
GSL_STATIC_LIBS_PATHS:INTERNAL=
//ADVANCED property for variable: GSL_VERSION
GSL_VERSION-ADVANCED:INTERNAL=1
GSL_VERSION:INTERNAL=2.5
GSL_gsl_INCLUDEDIR:INTERNAL=
GSL_gsl_LIBDIR:INTERNAL=
GSL_gsl_PREFIX:INTERNAL=
GSL_gsl_VERSION:INTERNAL=
//ADVANCED property for variable: INDI_INCLUDE_DIR
INDI_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: NOVA_INCLUDE_DIR
NOVA_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: NOVA_LIBRARIES
NOVA_LIBRARIES-ADVANCED:INTERNAL=1
PC_LIBUSB1_CFLAGS:INTERNAL=-I/usr/include/libusb-1.0
PC_LIBUSB1_CFLAGS_I:INTERNAL=
PC_LIBUSB1_CFLAGS_OTHER:INTERNAL=
PC_LIBUSB1_FOUND:INTERNAL=1
PC_LIBUSB1_INCLUDEDIR:INTERNAL=/usr/include
PC_LIBUSB1_INCLUDE_DIRS:INTERNAL=/usr/include/libusb-1.0
PC_LIBUSB1_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lusb-1.0
PC_LIBUSB1_LDFLAGS_OTHER:INTERNAL=
PC_LIBUSB1_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_LIBUSB1_LIBRARIES:INTERNAL=usb-1.0
PC_LIBUSB1_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_LIBUSB1_LIBS:INTERNAL=
PC_LIBUSB1_LIBS_L:INTERNAL=
PC_LIBUSB1_LIBS_OTHER:INTERNAL=
PC_LIBUSB1_LIBS_PATHS:INTERNAL=
PC_LIBUSB1_MODULE_NAME:INTERNAL=libusb-1.0
PC_LIBUSB1_PREFIX:INTERNAL=/usr
PC_LIBUSB1_STATIC_CFLAGS:INTERNAL=-I/usr/include/libusb-1.0
PC_LIBUSB1_STATIC_CFLAGS_I:INTERNAL=
PC_LIBUSB1_STATIC_CFLAGS_OTHER:INTERNAL=
PC_LIBUSB1_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/libusb-1.0
PC_LIBUSB1_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lusb-1.0;-ludev;-pthread
PC_LIBUSB1_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
PC_LIBUSB1_STATIC_LIBDIR:INTERNAL=
PC_LIBUSB1_STATIC_LIBRARIES:INTERNAL=usb-1.0;udev
PC_LIBUSB1_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
PC_LIBUSB1_STATIC_LIBS:INTERNAL=
PC_LIBUSB1_STATIC_LIBS_L:INTERNAL=
PC_LIBUSB1_STATIC_LIBS_OTHER:INTERNAL=
PC_LIBUSB1_STATIC_LIBS_PATHS:INTERNAL=
PC_LIBUSB1_VERSION:INTERNAL=1.0.23
PC_LIBUSB1_libusb-1.0_INCLUDEDIR:INTERNAL=
PC_LIBUSB1_libusb-1.0_LIBDIR:INTERNAL=
PC_LIBUSB1_libusb-1.0_PREFIX:INTERNAL=
PC_LIBUSB1_libusb-1.0_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: QHY_INCLUDE_DIR
QHY_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: QHY_LIBRARIES
QHY_LIBRARIES-ADVANCED:INTERNAL=1
//Test USB1_HAS_LIBUSB_ERROR_NAME
USB1_HAS_LIBUSB_ERROR_NAME:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//linker supports push/pop state
_CMAKE_LINKER_PUSHPOP_STATE_SUPPORTED:INTERNAL=TRUE
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr
__pkg_config_arguments_GSL:INTERNAL=QUIET;gsl
__pkg_config_arguments_PC_LIBUSB1:INTERNAL=QUIET;libusb-1.0
__pkg_config_checked_GSL:INTERNAL=1
__pkg_config_checked_PC_LIBUSB1:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GSL_gsl
pkgcfg_lib_GSL_gsl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GSL_gslcblas
pkgcfg_lib_GSL_gslcblas-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GSL_m
pkgcfg_lib_GSL_m-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_LIBUSB1_usb-1.0
pkgcfg_lib_PC_LIBUSB1_usb-1.0-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu

