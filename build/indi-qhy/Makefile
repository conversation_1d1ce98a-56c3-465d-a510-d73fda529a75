# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/indi-3rdparty/indi-qhy

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/build/indi-qhy

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/local/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles /home/<USER>/Projects/build/indi-qhy//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named indi_qhy_ccd

# Build rule for target.
indi_qhy_ccd: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 indi_qhy_ccd
.PHONY : indi_qhy_ccd

# fast build rule for target.
indi_qhy_ccd/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_ccd.dir/build.make CMakeFiles/indi_qhy_ccd.dir/build
.PHONY : indi_qhy_ccd/fast

#=============================================================================
# Target rules for targets named qhy_ccd_test

# Build rule for target.
qhy_ccd_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qhy_ccd_test
.PHONY : qhy_ccd_test

# fast build rule for target.
qhy_ccd_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_ccd_test.dir/build.make CMakeFiles/qhy_ccd_test.dir/build
.PHONY : qhy_ccd_test/fast

#=============================================================================
# Target rules for targets named qhy_video_test

# Build rule for target.
qhy_video_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qhy_video_test
.PHONY : qhy_video_test

# fast build rule for target.
qhy_video_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_video_test.dir/build.make CMakeFiles/qhy_video_test.dir/build
.PHONY : qhy_video_test/fast

#=============================================================================
# Target rules for targets named indi_qhy_focuser

# Build rule for target.
indi_qhy_focuser: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 indi_qhy_focuser
.PHONY : indi_qhy_focuser

# fast build rule for target.
indi_qhy_focuser/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_focuser.dir/build.make CMakeFiles/indi_qhy_focuser.dir/build
.PHONY : indi_qhy_focuser/fast

#=============================================================================
# Target rules for targets named indi_qhy_mount

# Build rule for target.
indi_qhy_mount: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 indi_qhy_mount
.PHONY : indi_qhy_mount

# fast build rule for target.
indi_qhy_mount/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/build
.PHONY : indi_qhy_mount/fast

eqmod/eqmodbase.o: eqmod/eqmodbase.cpp.o
.PHONY : eqmod/eqmodbase.o

# target to build an object file
eqmod/eqmodbase.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o
.PHONY : eqmod/eqmodbase.cpp.o

eqmod/eqmodbase.i: eqmod/eqmodbase.cpp.i
.PHONY : eqmod/eqmodbase.i

# target to preprocess a source file
eqmod/eqmodbase.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.i
.PHONY : eqmod/eqmodbase.cpp.i

eqmod/eqmodbase.s: eqmod/eqmodbase.cpp.s
.PHONY : eqmod/eqmodbase.s

# target to generate assembly for a file
eqmod/eqmodbase.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.s
.PHONY : eqmod/eqmodbase.cpp.s

eqmod/eqmoderror.o: eqmod/eqmoderror.cpp.o
.PHONY : eqmod/eqmoderror.o

# target to build an object file
eqmod/eqmoderror.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o
.PHONY : eqmod/eqmoderror.cpp.o

eqmod/eqmoderror.i: eqmod/eqmoderror.cpp.i
.PHONY : eqmod/eqmoderror.i

# target to preprocess a source file
eqmod/eqmoderror.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.i
.PHONY : eqmod/eqmoderror.cpp.i

eqmod/eqmoderror.s: eqmod/eqmoderror.cpp.s
.PHONY : eqmod/eqmoderror.s

# target to generate assembly for a file
eqmod/eqmoderror.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.s
.PHONY : eqmod/eqmoderror.cpp.s

eqmod/simulator/simulator.o: eqmod/simulator/simulator.cpp.o
.PHONY : eqmod/simulator/simulator.o

# target to build an object file
eqmod/simulator/simulator.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o
.PHONY : eqmod/simulator/simulator.cpp.o

eqmod/simulator/simulator.i: eqmod/simulator/simulator.cpp.i
.PHONY : eqmod/simulator/simulator.i

# target to preprocess a source file
eqmod/simulator/simulator.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.i
.PHONY : eqmod/simulator/simulator.cpp.i

eqmod/simulator/simulator.s: eqmod/simulator/simulator.cpp.s
.PHONY : eqmod/simulator/simulator.s

# target to generate assembly for a file
eqmod/simulator/simulator.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.s
.PHONY : eqmod/simulator/simulator.cpp.s

eqmod/simulator/skywatcher-simulator.o: eqmod/simulator/skywatcher-simulator.cpp.o
.PHONY : eqmod/simulator/skywatcher-simulator.o

# target to build an object file
eqmod/simulator/skywatcher-simulator.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o
.PHONY : eqmod/simulator/skywatcher-simulator.cpp.o

eqmod/simulator/skywatcher-simulator.i: eqmod/simulator/skywatcher-simulator.cpp.i
.PHONY : eqmod/simulator/skywatcher-simulator.i

# target to preprocess a source file
eqmod/simulator/skywatcher-simulator.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.i
.PHONY : eqmod/simulator/skywatcher-simulator.cpp.i

eqmod/simulator/skywatcher-simulator.s: eqmod/simulator/skywatcher-simulator.cpp.s
.PHONY : eqmod/simulator/skywatcher-simulator.s

# target to generate assembly for a file
eqmod/simulator/skywatcher-simulator.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.s
.PHONY : eqmod/simulator/skywatcher-simulator.cpp.s

eqmod/skywatcher.o: eqmod/skywatcher.cpp.o
.PHONY : eqmod/skywatcher.o

# target to build an object file
eqmod/skywatcher.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o
.PHONY : eqmod/skywatcher.cpp.o

eqmod/skywatcher.i: eqmod/skywatcher.cpp.i
.PHONY : eqmod/skywatcher.i

# target to preprocess a source file
eqmod/skywatcher.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.i
.PHONY : eqmod/skywatcher.cpp.i

eqmod/skywatcher.s: eqmod/skywatcher.cpp.s
.PHONY : eqmod/skywatcher.s

# target to generate assembly for a file
eqmod/skywatcher.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.s
.PHONY : eqmod/skywatcher.cpp.s

qhy_ccd.o: qhy_ccd.cpp.o
.PHONY : qhy_ccd.o

# target to build an object file
qhy_ccd.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_ccd.dir/build.make CMakeFiles/indi_qhy_ccd.dir/qhy_ccd.cpp.o
.PHONY : qhy_ccd.cpp.o

qhy_ccd.i: qhy_ccd.cpp.i
.PHONY : qhy_ccd.i

# target to preprocess a source file
qhy_ccd.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_ccd.dir/build.make CMakeFiles/indi_qhy_ccd.dir/qhy_ccd.cpp.i
.PHONY : qhy_ccd.cpp.i

qhy_ccd.s: qhy_ccd.cpp.s
.PHONY : qhy_ccd.s

# target to generate assembly for a file
qhy_ccd.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_ccd.dir/build.make CMakeFiles/indi_qhy_ccd.dir/qhy_ccd.cpp.s
.PHONY : qhy_ccd.cpp.s

qhy_ccd_test.o: qhy_ccd_test.cpp.o
.PHONY : qhy_ccd_test.o

# target to build an object file
qhy_ccd_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_ccd_test.dir/build.make CMakeFiles/qhy_ccd_test.dir/qhy_ccd_test.cpp.o
.PHONY : qhy_ccd_test.cpp.o

qhy_ccd_test.i: qhy_ccd_test.cpp.i
.PHONY : qhy_ccd_test.i

# target to preprocess a source file
qhy_ccd_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_ccd_test.dir/build.make CMakeFiles/qhy_ccd_test.dir/qhy_ccd_test.cpp.i
.PHONY : qhy_ccd_test.cpp.i

qhy_ccd_test.s: qhy_ccd_test.cpp.s
.PHONY : qhy_ccd_test.s

# target to generate assembly for a file
qhy_ccd_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_ccd_test.dir/build.make CMakeFiles/qhy_ccd_test.dir/qhy_ccd_test.cpp.s
.PHONY : qhy_ccd_test.cpp.s

qhy_focuser.o: qhy_focuser.cpp.o
.PHONY : qhy_focuser.o

# target to build an object file
qhy_focuser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_focuser.dir/build.make CMakeFiles/indi_qhy_focuser.dir/qhy_focuser.cpp.o
.PHONY : qhy_focuser.cpp.o

qhy_focuser.i: qhy_focuser.cpp.i
.PHONY : qhy_focuser.i

# target to preprocess a source file
qhy_focuser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_focuser.dir/build.make CMakeFiles/indi_qhy_focuser.dir/qhy_focuser.cpp.i
.PHONY : qhy_focuser.cpp.i

qhy_focuser.s: qhy_focuser.cpp.s
.PHONY : qhy_focuser.s

# target to generate assembly for a file
qhy_focuser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_focuser.dir/build.make CMakeFiles/indi_qhy_focuser.dir/qhy_focuser.cpp.s
.PHONY : qhy_focuser.cpp.s

qhy_mount.o: qhy_mount.cpp.o
.PHONY : qhy_mount.o

# target to build an object file
qhy_mount.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o
.PHONY : qhy_mount.cpp.o

qhy_mount.i: qhy_mount.cpp.i
.PHONY : qhy_mount.i

# target to preprocess a source file
qhy_mount.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.i
.PHONY : qhy_mount.cpp.i

qhy_mount.s: qhy_mount.cpp.s
.PHONY : qhy_mount.s

# target to generate assembly for a file
qhy_mount.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.s
.PHONY : qhy_mount.cpp.s

qhy_mount_base.o: qhy_mount_base.cpp.o
.PHONY : qhy_mount_base.o

# target to build an object file
qhy_mount_base.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o
.PHONY : qhy_mount_base.cpp.o

qhy_mount_base.i: qhy_mount_base.cpp.i
.PHONY : qhy_mount_base.i

# target to preprocess a source file
qhy_mount_base.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.i
.PHONY : qhy_mount_base.cpp.i

qhy_mount_base.s: qhy_mount_base.cpp.s
.PHONY : qhy_mount_base.s

# target to generate assembly for a file
qhy_mount_base.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.s
.PHONY : qhy_mount_base.cpp.s

qhy_video_test.o: qhy_video_test.cpp.o
.PHONY : qhy_video_test.o

# target to build an object file
qhy_video_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_video_test.dir/build.make CMakeFiles/qhy_video_test.dir/qhy_video_test.cpp.o
.PHONY : qhy_video_test.cpp.o

qhy_video_test.i: qhy_video_test.cpp.i
.PHONY : qhy_video_test.i

# target to preprocess a source file
qhy_video_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_video_test.dir/build.make CMakeFiles/qhy_video_test.dir/qhy_video_test.cpp.i
.PHONY : qhy_video_test.cpp.i

qhy_video_test.s: qhy_video_test.cpp.s
.PHONY : qhy_video_test.s

# target to generate assembly for a file
qhy_video_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_video_test.dir/build.make CMakeFiles/qhy_video_test.dir/qhy_video_test.cpp.s
.PHONY : qhy_video_test.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... indi_qhy_ccd"
	@echo "... indi_qhy_focuser"
	@echo "... indi_qhy_mount"
	@echo "... qhy_ccd_test"
	@echo "... qhy_video_test"
	@echo "... eqmod/eqmodbase.o"
	@echo "... eqmod/eqmodbase.i"
	@echo "... eqmod/eqmodbase.s"
	@echo "... eqmod/eqmoderror.o"
	@echo "... eqmod/eqmoderror.i"
	@echo "... eqmod/eqmoderror.s"
	@echo "... eqmod/simulator/simulator.o"
	@echo "... eqmod/simulator/simulator.i"
	@echo "... eqmod/simulator/simulator.s"
	@echo "... eqmod/simulator/skywatcher-simulator.o"
	@echo "... eqmod/simulator/skywatcher-simulator.i"
	@echo "... eqmod/simulator/skywatcher-simulator.s"
	@echo "... eqmod/skywatcher.o"
	@echo "... eqmod/skywatcher.i"
	@echo "... eqmod/skywatcher.s"
	@echo "... qhy_ccd.o"
	@echo "... qhy_ccd.i"
	@echo "... qhy_ccd.s"
	@echo "... qhy_ccd_test.o"
	@echo "... qhy_ccd_test.i"
	@echo "... qhy_ccd_test.s"
	@echo "... qhy_focuser.o"
	@echo "... qhy_focuser.i"
	@echo "... qhy_focuser.s"
	@echo "... qhy_mount.o"
	@echo "... qhy_mount.i"
	@echo "... qhy_mount.s"
	@echo "... qhy_mount_base.o"
	@echo "... qhy_mount_base.i"
	@echo "... qhy_mount_base.s"
	@echo "... qhy_video_test.o"
	@echo "... qhy_video_test.i"
	@echo "... qhy_video_test.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

