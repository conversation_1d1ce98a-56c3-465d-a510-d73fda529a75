# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/indi-3rdparty/indi-qhy

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/build/indi-qhy

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/indi_qhy_ccd.dir/all
all: CMakeFiles/qhy_ccd_test.dir/all
all: CMakeFiles/qhy_video_test.dir/all
all: CMakeFiles/indi_qhy_focuser.dir/all
all: CMakeFiles/indi_qhy_mount.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/indi_qhy_ccd.dir/clean
clean: CMakeFiles/qhy_ccd_test.dir/clean
clean: CMakeFiles/qhy_video_test.dir/clean
clean: CMakeFiles/indi_qhy_focuser.dir/clean
clean: CMakeFiles/indi_qhy_mount.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/indi_qhy_ccd.dir

# All Build rule for target.
CMakeFiles/indi_qhy_ccd.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_ccd.dir/build.make CMakeFiles/indi_qhy_ccd.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_ccd.dir/build.make CMakeFiles/indi_qhy_ccd.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=1,2 "Built target indi_qhy_ccd"
.PHONY : CMakeFiles/indi_qhy_ccd.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/indi_qhy_ccd.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/indi_qhy_ccd.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 0
.PHONY : CMakeFiles/indi_qhy_ccd.dir/rule

# Convenience name for target.
indi_qhy_ccd: CMakeFiles/indi_qhy_ccd.dir/rule
.PHONY : indi_qhy_ccd

# clean rule for target.
CMakeFiles/indi_qhy_ccd.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_ccd.dir/build.make CMakeFiles/indi_qhy_ccd.dir/clean
.PHONY : CMakeFiles/indi_qhy_ccd.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/qhy_ccd_test.dir

# All Build rule for target.
CMakeFiles/qhy_ccd_test.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_ccd_test.dir/build.make CMakeFiles/qhy_ccd_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_ccd_test.dir/build.make CMakeFiles/qhy_ccd_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=13,14 "Built target qhy_ccd_test"
.PHONY : CMakeFiles/qhy_ccd_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/qhy_ccd_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/qhy_ccd_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 0
.PHONY : CMakeFiles/qhy_ccd_test.dir/rule

# Convenience name for target.
qhy_ccd_test: CMakeFiles/qhy_ccd_test.dir/rule
.PHONY : qhy_ccd_test

# clean rule for target.
CMakeFiles/qhy_ccd_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_ccd_test.dir/build.make CMakeFiles/qhy_ccd_test.dir/clean
.PHONY : CMakeFiles/qhy_ccd_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/qhy_video_test.dir

# All Build rule for target.
CMakeFiles/qhy_video_test.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_video_test.dir/build.make CMakeFiles/qhy_video_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_video_test.dir/build.make CMakeFiles/qhy_video_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=15,16 "Built target qhy_video_test"
.PHONY : CMakeFiles/qhy_video_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/qhy_video_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/qhy_video_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 0
.PHONY : CMakeFiles/qhy_video_test.dir/rule

# Convenience name for target.
qhy_video_test: CMakeFiles/qhy_video_test.dir/rule
.PHONY : qhy_video_test

# clean rule for target.
CMakeFiles/qhy_video_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/qhy_video_test.dir/build.make CMakeFiles/qhy_video_test.dir/clean
.PHONY : CMakeFiles/qhy_video_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/indi_qhy_focuser.dir

# All Build rule for target.
CMakeFiles/indi_qhy_focuser.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_focuser.dir/build.make CMakeFiles/indi_qhy_focuser.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_focuser.dir/build.make CMakeFiles/indi_qhy_focuser.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=3,4 "Built target indi_qhy_focuser"
.PHONY : CMakeFiles/indi_qhy_focuser.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/indi_qhy_focuser.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/indi_qhy_focuser.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 0
.PHONY : CMakeFiles/indi_qhy_focuser.dir/rule

# Convenience name for target.
indi_qhy_focuser: CMakeFiles/indi_qhy_focuser.dir/rule
.PHONY : indi_qhy_focuser

# clean rule for target.
CMakeFiles/indi_qhy_focuser.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_focuser.dir/build.make CMakeFiles/indi_qhy_focuser.dir/clean
.PHONY : CMakeFiles/indi_qhy_focuser.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/indi_qhy_mount.dir

# All Build rule for target.
CMakeFiles/indi_qhy_mount.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=5,6,7,8,9,10,11,12 "Built target indi_qhy_mount"
.PHONY : CMakeFiles/indi_qhy_mount.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/indi_qhy_mount.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/indi_qhy_mount.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Projects/build/indi-qhy/CMakeFiles 0
.PHONY : CMakeFiles/indi_qhy_mount.dir/rule

# Convenience name for target.
indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/rule
.PHONY : indi_qhy_mount

# clean rule for target.
CMakeFiles/indi_qhy_mount.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/indi_qhy_mount.dir/build.make CMakeFiles/indi_qhy_mount.dir/clean
.PHONY : CMakeFiles/indi_qhy_mount.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

