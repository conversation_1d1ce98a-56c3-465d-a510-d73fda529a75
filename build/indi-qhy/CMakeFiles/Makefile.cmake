# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "CMakeFiles/3.29.0/CMakeCCompiler.cmake"
  "CMakeFiles/3.29.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.29.0/CMakeSystem.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/CMakeCommon.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/CMakeParseArguments.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/FindCFITSIO.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/FindGSL.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/FindINDI.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/FindNova.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/FindPackageHandleStandardArgs.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/FindPackageMessage.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/FindQHY.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/FindUSB1.cmake"
  "/home/<USER>/Projects/indi-3rdparty/cmake_modules/UnityBuild.cmake"
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/CMakeLists.txt"
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/config.h.cmake"
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/indi_qhy.xml.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeCInformation.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeCXXInformation.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeGenericSystem.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeInitializeConfigs.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeLanguageInformation.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakePushCheckState.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/local/share/cmake-3.29/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckCCompilerFlag.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckCSourceCompiles.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckIncludeFile.cmake"
  "/usr/local/share/cmake-3.29/Modules/CheckLibraryExists.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GNU-C.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GNU-CXX.cmake"
  "/usr/local/share/cmake-3.29/Modules/Compiler/GNU.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindPackageMessage.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindPkgConfig.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindThreads.cmake"
  "/usr/local/share/cmake-3.29/Modules/FindZLIB.cmake"
  "/usr/local/share/cmake-3.29/Modules/GNUInstallDirs.cmake"
  "/usr/local/share/cmake-3.29/Modules/Internal/CheckCompilerFlag.cmake"
  "/usr/local/share/cmake-3.29/Modules/Internal/CheckFlagCommonConfig.cmake"
  "/usr/local/share/cmake-3.29/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux-GNU.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux-Initialize.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/Linux.cmake"
  "/usr/local/share/cmake-3.29/Modules/Platform/UnixPaths.cmake"
  "/usr/local/share/cmake-3.29/Modules/SelectLibraryConfigurations.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "config.h"
  "indi_qhy.xml"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/indi_qhy_ccd.dir/DependInfo.cmake"
  "CMakeFiles/qhy_ccd_test.dir/DependInfo.cmake"
  "CMakeFiles/qhy_video_test.dir/DependInfo.cmake"
  "CMakeFiles/indi_qhy_focuser.dir/DependInfo.cmake"
  "CMakeFiles/indi_qhy_mount.dir/DependInfo.cmake"
  "CMakeFiles/qhy_mount_test.dir/DependInfo.cmake"
  "CMakeFiles/qhy_mount_simple_test.dir/DependInfo.cmake"
  )
