# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/indi-3rdparty/indi-qhy

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/build/indi-qhy

# Include any dependencies generated for this target.
include CMakeFiles/qhy_mount_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/qhy_mount_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/qhy_mount_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/qhy_mount_test.dir/flags.make

CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o: CMakeFiles/qhy_mount_test.dir/flags.make
CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_test.cpp
CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o: CMakeFiles/qhy_mount_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o -MF CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o.d -o CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_test.cpp

CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_test.cpp > CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.i

CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_test.cpp -o CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.s

CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o: CMakeFiles/qhy_mount_test.dir/flags.make
CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_base.cpp
CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o: CMakeFiles/qhy_mount_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o -MF CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o.d -o CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_base.cpp

CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_base.cpp > CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.i

CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_base.cpp -o CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.s

CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o: CMakeFiles/qhy_mount_test.dir/flags.make
CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmodbase.cpp
CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o: CMakeFiles/qhy_mount_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o -MF CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o.d -o CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmodbase.cpp

CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmodbase.cpp > CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.i

CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmodbase.cpp -o CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.s

CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o: CMakeFiles/qhy_mount_test.dir/flags.make
CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmoderror.cpp
CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o: CMakeFiles/qhy_mount_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o -MF CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o.d -o CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmoderror.cpp

CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmoderror.cpp > CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.i

CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmoderror.cpp -o CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.s

CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o: CMakeFiles/qhy_mount_test.dir/flags.make
CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/skywatcher.cpp
CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o: CMakeFiles/qhy_mount_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o -MF CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o.d -o CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/skywatcher.cpp

CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/skywatcher.cpp > CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.i

CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/skywatcher.cpp -o CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.s

CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o: CMakeFiles/qhy_mount_test.dir/flags.make
CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/simulator.cpp
CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o: CMakeFiles/qhy_mount_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o -MF CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o.d -o CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/simulator.cpp

CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/simulator.cpp > CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.i

CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/simulator.cpp -o CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.s

CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o: CMakeFiles/qhy_mount_test.dir/flags.make
CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/skywatcher-simulator.cpp
CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o: CMakeFiles/qhy_mount_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o -MF CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o.d -o CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/skywatcher-simulator.cpp

CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/skywatcher-simulator.cpp > CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.i

CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/skywatcher-simulator.cpp -o CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.s

# Object files for target qhy_mount_test
qhy_mount_test_OBJECTS = \
"CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o" \
"CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o" \
"CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o" \
"CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o" \
"CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o" \
"CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o" \
"CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o"

# External object files for target qhy_mount_test
qhy_mount_test_EXTERNAL_OBJECTS =

qhy_mount_test: CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o
qhy_mount_test: CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o
qhy_mount_test: CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o
qhy_mount_test: CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o
qhy_mount_test: CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o
qhy_mount_test: CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o
qhy_mount_test: CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o
qhy_mount_test: CMakeFiles/qhy_mount_test.dir/build.make
qhy_mount_test: /usr/lib/x86_64-linux-gnu/libindidriver.so
qhy_mount_test: /usr/lib/x86_64-linux-gnu/libindiAlignmentDriver.so
qhy_mount_test: /usr/lib/x86_64-linux-gnu/libnova.so
qhy_mount_test: /usr/lib/x86_64-linux-gnu/libgsl.so
qhy_mount_test: /usr/lib/x86_64-linux-gnu/libgslcblas.so
qhy_mount_test: /usr/lib/x86_64-linux-gnu/libz.so
qhy_mount_test: CMakeFiles/qhy_mount_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX executable qhy_mount_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/qhy_mount_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/qhy_mount_test.dir/build: qhy_mount_test
.PHONY : CMakeFiles/qhy_mount_test.dir/build

CMakeFiles/qhy_mount_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/qhy_mount_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/qhy_mount_test.dir/clean

CMakeFiles/qhy_mount_test.dir/depend:
	cd /home/<USER>/Projects/build/indi-qhy && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Projects/indi-3rdparty/indi-qhy /home/<USER>/Projects/indi-3rdparty/indi-qhy /home/<USER>/Projects/build/indi-qhy /home/<USER>/Projects/build/indi-qhy /home/<USER>/Projects/build/indi-qhy/CMakeFiles/qhy_mount_test.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/qhy_mount_test.dir/depend

