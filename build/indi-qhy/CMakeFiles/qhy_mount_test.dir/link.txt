/usr/bin/c++  -D_FORTIFY_SOURCE=2 -fstack-protector-all -fPIE -Wa,--noexecstack  -Wall -Wextra -Wno-unused-but-set-variable -Wno-format-truncation -g -Wno-error -g   -Wl,-z,nodump -Wl,-z,noexecstack -Wl,-z,relro -Wl,-z,now -pie  -Wl,--no-as-needed CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o "CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o" -o qhy_mount_test  /usr/lib/x86_64-linux-gnu/libindidriver.so /usr/lib/x86_64-linux-gnu/libindiAlignmentDriver.so /usr/lib/x86_64-linux-gnu/libnova.so /usr/lib/x86_64-linux-gnu/libgsl.so /usr/lib/x86_64-linux-gnu/libgslcblas.so /usr/lib/x86_64-linux-gnu/libz.so -lrt
