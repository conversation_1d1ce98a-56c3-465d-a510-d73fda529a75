
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmodbase.cpp" "CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o" "gcc" "CMakeFiles/qhy_mount_test.dir/eqmod/eqmodbase.cpp.o.d"
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmoderror.cpp" "CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o" "gcc" "CMakeFiles/qhy_mount_test.dir/eqmod/eqmoderror.cpp.o.d"
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/simulator.cpp" "CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o" "gcc" "CMakeFiles/qhy_mount_test.dir/eqmod/simulator/simulator.cpp.o.d"
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/skywatcher-simulator.cpp" "CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o" "gcc" "CMakeFiles/qhy_mount_test.dir/eqmod/simulator/skywatcher-simulator.cpp.o.d"
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/skywatcher.cpp" "CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o" "gcc" "CMakeFiles/qhy_mount_test.dir/eqmod/skywatcher.cpp.o.d"
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_base.cpp" "CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o" "gcc" "CMakeFiles/qhy_mount_test.dir/qhy_mount_base.cpp.o.d"
  "/home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_test.cpp" "CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o" "gcc" "CMakeFiles/qhy_mount_test.dir/qhy_mount_test.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
