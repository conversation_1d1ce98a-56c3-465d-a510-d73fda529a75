# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DCALLBACK_MODE_SUPPORT -D__CPP_MODE__

CXX_INCLUDES = -I/home/<USER>/Projects/build/indi-qhy -I/home/<USER>/Projects/indi-3rdparty/indi-qhy -I/usr/include/libindi -I/usr/include/libusb-1.0

CXX_FLAGS =  -D_FORTIFY_SOURCE=2 -fstack-protector-all -fPIE -Wa,--noexecstack  -Wall -Wextra -Wno-unused-but-set-variable -Wno-format-truncation -g -Wno-error -g -std=gnu++17 -fPIE

