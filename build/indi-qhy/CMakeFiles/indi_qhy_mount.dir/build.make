# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.29

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Projects/indi-3rdparty/indi-qhy

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Projects/build/indi-qhy

# Include any dependencies generated for this target.
include CMakeFiles/indi_qhy_mount.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/indi_qhy_mount.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/indi_qhy_mount.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/indi_qhy_mount.dir/flags.make

CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o: CMakeFiles/indi_qhy_mount.dir/flags.make
CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount.cpp
CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o: CMakeFiles/indi_qhy_mount.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o -MF CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o.d -o CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount.cpp

CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount.cpp > CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.i

CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount.cpp -o CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.s

CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o: CMakeFiles/indi_qhy_mount.dir/flags.make
CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_base.cpp
CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o: CMakeFiles/indi_qhy_mount.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o -MF CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o.d -o CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_base.cpp

CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_base.cpp > CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.i

CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/qhy_mount_base.cpp -o CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.s

CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o: CMakeFiles/indi_qhy_mount.dir/flags.make
CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmodbase.cpp
CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o: CMakeFiles/indi_qhy_mount.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o -MF CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o.d -o CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmodbase.cpp

CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmodbase.cpp > CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.i

CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmodbase.cpp -o CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.s

CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o: CMakeFiles/indi_qhy_mount.dir/flags.make
CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmoderror.cpp
CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o: CMakeFiles/indi_qhy_mount.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o -MF CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o.d -o CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmoderror.cpp

CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmoderror.cpp > CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.i

CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/eqmoderror.cpp -o CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.s

CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o: CMakeFiles/indi_qhy_mount.dir/flags.make
CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/skywatcher.cpp
CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o: CMakeFiles/indi_qhy_mount.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o -MF CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o.d -o CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/skywatcher.cpp

CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/skywatcher.cpp > CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.i

CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/skywatcher.cpp -o CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.s

CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o: CMakeFiles/indi_qhy_mount.dir/flags.make
CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/simulator.cpp
CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o: CMakeFiles/indi_qhy_mount.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o -MF CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o.d -o CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/simulator.cpp

CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/simulator.cpp > CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.i

CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/simulator.cpp -o CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.s

CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o: CMakeFiles/indi_qhy_mount.dir/flags.make
CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o: /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/skywatcher-simulator.cpp
CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o: CMakeFiles/indi_qhy_mount.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o -MF CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o.d -o CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o -c /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/skywatcher-simulator.cpp

CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/skywatcher-simulator.cpp > CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.i

CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Projects/indi-3rdparty/indi-qhy/eqmod/simulator/skywatcher-simulator.cpp -o CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.s

# Object files for target indi_qhy_mount
indi_qhy_mount_OBJECTS = \
"CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o" \
"CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o" \
"CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o" \
"CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o" \
"CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o" \
"CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o" \
"CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o"

# External object files for target indi_qhy_mount
indi_qhy_mount_EXTERNAL_OBJECTS =

indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/qhy_mount.cpp.o
indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/qhy_mount_base.cpp.o
indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/eqmod/eqmodbase.cpp.o
indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/eqmod/eqmoderror.cpp.o
indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/eqmod/skywatcher.cpp.o
indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/simulator.cpp.o
indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/eqmod/simulator/skywatcher-simulator.cpp.o
indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/build.make
indi_qhy_mount: /usr/lib/x86_64-linux-gnu/libindidriver.so
indi_qhy_mount: /usr/lib/x86_64-linux-gnu/libindiAlignmentDriver.so
indi_qhy_mount: /usr/lib/x86_64-linux-gnu/libnova.so
indi_qhy_mount: /usr/lib/x86_64-linux-gnu/libgsl.so
indi_qhy_mount: /usr/lib/x86_64-linux-gnu/libgslcblas.so
indi_qhy_mount: /usr/lib/x86_64-linux-gnu/libz.so
indi_qhy_mount: CMakeFiles/indi_qhy_mount.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/Projects/build/indi-qhy/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking CXX executable indi_qhy_mount"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/indi_qhy_mount.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/indi_qhy_mount.dir/build: indi_qhy_mount
.PHONY : CMakeFiles/indi_qhy_mount.dir/build

CMakeFiles/indi_qhy_mount.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/indi_qhy_mount.dir/cmake_clean.cmake
.PHONY : CMakeFiles/indi_qhy_mount.dir/clean

CMakeFiles/indi_qhy_mount.dir/depend:
	cd /home/<USER>/Projects/build/indi-qhy && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Projects/indi-3rdparty/indi-qhy /home/<USER>/Projects/indi-3rdparty/indi-qhy /home/<USER>/Projects/build/indi-qhy /home/<USER>/Projects/build/indi-qhy /home/<USER>/Projects/build/indi-qhy/CMakeFiles/indi_qhy_mount.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/indi_qhy_mount.dir/depend

