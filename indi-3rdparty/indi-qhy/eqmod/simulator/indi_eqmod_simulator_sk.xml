<INDIDriver>
<defNumberVector device="EQMod Mount" name="SIMULATORWORM" label="Worm Gear" group="Simulation" state="Idle" perm="rw">
<defNumber name="RA_TEETH" label="RA Teeth" format="%.0f" min="1.0" max="10000.0" step="1.0">
180.0
</defNumber>
<defNumber name="DE_TEETH" label="DE Teeth" format="%.0f" min="1.0" max="10000.0" step="1.0">
180.0
</defNumber>
</defNumberVector>
<defNumberVector device="EQMod Mount" name="SIMULATORRATIO" label="Gear Ratio" group="Simulation" state="Idle" perm="rw">
<defNumber name="RA_RATIO_NUM" label="RA Worm Teeth" format="%.0f" min="1.0" max="10000.0" step="1.0">
47.0
</defNumber>
<defNumber name="RA_RATIO_DEN" label="RA Motor Teeth" format="%.0f" min="1.0" max="10000.0" step="1.0">
12.0
</defNumber>
<defNumber name="DE_RATIO_NUM" label="DE Worm Teeth" format="%.0f" min="1.0" max="10000.0" step="1.0">
47.0
</defNumber>
<defNumber name="DE_RATIO_DEN" label="DE Motor Teeth" format="%.0f" min="1.0" max="10000.0" step="1.0">
12.0
</defNumber>
</defNumberVector>
<defNumberVector device="EQMod Mount" name="SIMULATORMOTOR" label="Motors" group="Simulation" state="Idle" perm="rw">
<defNumber name="RA_MOTOR_STEPS" label="RA Motor steps" format="%.0f" min="1.0" max="10000.0" step="1.0">
200.0
</defNumber>
<defNumber name="RA_MOTOR_USTEPS" label="RA microsteps" format="%.0f" min="1.0" max="10000.0" step="1.0">
64.0
</defNumber>
<defNumber name="DE_MOTOR_STEPS" label="DE Motor steps" format="%.0f" min="1.0" max="10000.0" step="1.0">
200.0
</defNumber>
<defNumber name="DE_MOTOR_USTEPS" label="DE microsteps" format="%.0f" min="1.0" max="10000.0" step="1.0">
64.0
</defNumber>
</defNumberVector>
<defSwitchVector device="EQMod Mount" name="SIMULATORMODE" label="Predefined Mode" group="Simulation" state="Idle" perm="rw" rule="OneOfMany">
<defSwitch name="SIM_EQ6" label="EQ6">
On
</defSwitch>
<defSwitch name="SIM_HEQ5" label="HEQ5">
Off
</defSwitch>
<defSwitch name="SIM_NEQ5" label="NEQ5">
Off
</defSwitch>
<defSwitch name="SIM_NEQ3" label="NEQ3">
Off
</defSwitch>
<defSwitch name="SIM_GEEHALEL" label="GEEHALEL">
Off
</defSwitch>
<defSwitch name="SIM_CUSTOM" label="CUSTOM">
Off
</defSwitch>
</defSwitchVector>
<defSwitchVector device="EQMod Mount" name="SIMULATORHIGHSPEED" label="HighSpeed Stepping" group="Simulation" state="Idle" perm="rw" rule="OneOfMany">
<defSwitch name="SIM_FULLSTEP" label="Full Step">
Off
</defSwitch>
<defSwitch name="SIM_HALFSTEP" label="Half Step">
On
</defSwitch>
</defSwitchVector>
<defTextVector device="EQMod Mount" name="SIMULATORMCVERSION" label="MC Version" group="Simulation" state="Idle" perm="rw">
<defText name="SIM_MCPHRASE" label="MC Phrase">020300</defText> 
</defTextVector>
</INDIDriver>
